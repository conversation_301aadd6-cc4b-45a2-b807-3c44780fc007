<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="view_quality_processing_search" model="ir.ui.view">
        <field name="name">quality.processing.search</field>
        <field name="model">roke.quality.processing</field>
        <field name="arch" type="xml">
            <search>
                <field name="code"/>
                <field name="processing_type"/>
                <field name="quality_order_id"/>
                <field name="employee_id"/>
                <field name="state"/>
            </search>
        </field>
    </record>
    <record id="view_quality_processing_tree" model="ir.ui.view">
        <field name="name">quality.processing.tree</field>
        <field name="model">roke.quality.processing</field>
        <field name="arch" type="xml">
            <tree>
                <field name="code"/>
                <field name="processing_type"/>
                <field name="quality_order_id"/>
                <field name="employee_id"/>
                <field name="processing_date"/>
                <field name="quantity"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <record id="view_quality_processing_form" model="ir.ui.view">
        <field name="name">quality.processing.form</field>
        <field name="model">roke.quality.processing</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_confirm" string="确认" type="object" class="oe_highlight"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <group col="3">
                    <group>
                        <field name="code" readonly="1"/>
                        <field name="processing_type"/>
                        <field name="quality_order_id"/>
<!--                        <field name="state"/>-->
                    </group>
                    <group>
                        <field name="task_id"/>
                        <field name="picking_id"/>
                        <field name="sale_picking_id"/>
                    </group>
                    <group>
                        <field name="employee_id"/>
                        <field name="processing_date"/>
                        <field name="quantity"/>
                    </group>
                </group>
                <notebook>
                    <page string="不合格明细">
                        <field name="line_ids">
                            <tree editable="bottom">
                                <field name="product_id"/>
                                <field name="unqualified_qty"/>
                                <field name="note"/>
                            </tree>
                        </field>
                    </page>
                </notebook>
            </form>
        </field>
    </record>

    <record id="action_quality_processing" model="ir.actions.act_window">
        <field name="name">质检处理单</field>
        <field name="res_model">roke.quality.processing</field>
        <field name="view_mode">tree,form</field>
    </record>
    <menuitem id="menu_quality_processing"
              name="质检处理单"
              parent="roke_mes_quality.roke_quality_business_menu"
              action="action_quality_processing"
              groups="base.group_system"
              sequence="40"
    />

</odoo>