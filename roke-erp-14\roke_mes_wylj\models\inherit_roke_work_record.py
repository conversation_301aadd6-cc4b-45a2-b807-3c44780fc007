from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from odoo.exceptions import UserError

class InheritRokeWorkRecord(models.Model):
    _inherit = "roke.work.record"

    mold_id = fields.Many2one(related="work_order_id.mold_id", string='模具', store=True)
    tool_id = fields.Many2one(related="work_order_id.mold_id", string='工装', store=True)
    equipment_id = fields.Many2one(related="work_order_id.mold_id", string="设备", store=True)
    workshop_id = fields.Many2one(related="work_order_id.workshop_id", string='指派车间', store=True)
    container_code = fields.Char(string='容器编号')