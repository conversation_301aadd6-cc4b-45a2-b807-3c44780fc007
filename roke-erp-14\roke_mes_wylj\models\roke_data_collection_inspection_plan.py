from odoo import api, fields, models


class RokeDataCollectionInspectionPlan(models.Model):
    _name = 'roke.data.collection.inspection.plan'
    _description = '数采点检方案'
    _rec_name = 'name'

    code = fields.Char(string='方案编号', default=lambda self: self.env['ir.sequence'].next_by_code(
        'roke.data.collection.inspection.plan.code'))
    name = fields.Char(string='方案名称')
    equipment_id = fields.Many2one('roke.mes.equipment', string='设备', required=True)
    note = fields.Text(string='备注')
    active = fields.Boolean(string='启用', default=True)
    line_ids = fields.One2many('roke.data.collection.inspection.plan.line', 'plan_id', string='数采点检项')

    def multi_add_inspection_action(self):
        return {
            'name': '批量添加数采点检项',
            'type': 'ir.actions.act_window',
            'res_model': 'add.collection.inspection.plan',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_plan_id': self.id
            }
        }


class RokeDataCollectionInspectionPlanLine(models.Model):
    _name = 'roke.data.collection.inspection.plan.line'
    _description = '数采点检方案明细'

    plan_id = fields.Many2one('roke.data.collection.inspection.plan', string='点检方案', required=True,
                              ondelete='cascade')
    check_item = fields.Many2one("roke.mes.equipment.item", string='数采点检项')
    item_id = fields.Char(string='检查项id', related='check_item.item_id', store=True)
