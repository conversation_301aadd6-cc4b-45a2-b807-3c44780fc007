from odoo import models, fields, api


class RokeSelfCheckOrder(models.Model):
    _name = 'roke.self.check.order'
    _description = '自检单'

    # 基础字段
    task_id = fields.Many2one('roke.production.task', string='生产任务')
    work_order_id = fields.Many2one('roke.work.order', string='生产工单')
    employee_id = fields.Many2one('roke.employee', string='自检人', default=lambda self: self.env.user)
    check_date = fields.Datetime(string='自检时间', default=fields.Datetime.now())
    product_id = fields.Many2one("roke.product" ,string='产品')

    # 数量相关字段
    check_qty = fields.Float(string='自检数量')
    qualified_qty = fields.Float(string='合格数量')
    unqualified_qty = fields.Float(string='不合格数量', compute='_compute_unqualified_qty', store=True)

    # 状态
    state = fields.Selection([
        ('草稿', '草稿'),
        ('已确认', '已确认'),
        ('已取消', '已取消')
    ], string='状态', default='草稿')

    # 计算字段
    @api.depends('check_qty', 'qualified_qty')
    def _compute_unqualified_qty(self):
        for record in self:
            record.unqualified_qty = record.check_qty - record.qualified_qty
