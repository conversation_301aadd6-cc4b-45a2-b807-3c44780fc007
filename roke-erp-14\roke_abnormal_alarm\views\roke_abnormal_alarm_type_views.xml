<?xml version="1.0"?>
<odoo>
    <record id="roke_abnormal_alarm_type_views_tree" model="ir.ui.view">
        <field name="name">roke.abnormal.alarm.type.tree</field>
        <field name="model">roke.abnormal.alarm.type</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="note"/>
            </tree>
        </field>
    </record>

    <record id="roke_abnormal_alarm_type_views_form" model="ir.ui.view">
        <field name="name">roke.abnormal.alarm.type.form</field>
        <field name="model">roke.abnormal.alarm.type</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                        </group>
                        <group>
                            <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="异常项目">
                            <field name="item_ids">
                                <tree editable="bottom">
                                    <field name="type_id" invisible="1"/>
                                    <field name="code" readonly="1"/>
                                    <field name="name"/>
                                    <field name="department_id" options="{'no_create': True}"/>
                                    <field name="employee_ids" widget="many2many_tags" options="{'no_create': True}"/>
                                    <field name="note"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                    <field name="note"/>
                </sheet>
            </form>
        </field>
    </record>

    <record id="roke_abnormal_alarm_type_views_action" model="ir.actions.act_window">
        <field name="name">异常类型</field>
        <field name="res_model">roke.abnormal.alarm.type</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
    </record>
</odoo>