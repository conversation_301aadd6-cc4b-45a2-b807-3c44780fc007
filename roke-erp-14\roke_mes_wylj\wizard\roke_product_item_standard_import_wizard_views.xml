<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="roke_product_item_standard_import_wizard_form" model="ir.ui.view">
        <!-- 修正模型名称与Python代码一致 -->
        <field name="model">roke.product.item.standard.import.wizard</field>
        <field name="arch" type="xml">
            <form>
                <group string="导出模板">
                    <div style="color: #a0a0a0;">
                        <p>
                            您可以从此处下载导入模板。
                            <!-- 添加title属性提升用户体验 -->
                            <button name="action_export_data" icon="fa-download" string="导出模板"
                                    type="object" class="btn-link" title="点击下载Excel模板"/>
                        </p>
                    </div>
                </group>
                <group string="导入Excel文件">
                    <!-- 添加帮助文本 -->
                    <field name="excel_file" help="请上传符合模板格式的Excel文件"/>
                </group>
                <footer>
                    <!-- 为确认按钮添加确认对话框 -->
                    <button name="import_collection_standard" string="确认" type="object"
                            class="oe_highlight"/>
                    <button string="取消" class="oe_link" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>
</odoo>