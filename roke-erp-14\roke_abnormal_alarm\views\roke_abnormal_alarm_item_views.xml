<?xml version="1.0"?>
<odoo>
    <record id="roke_abnormal_alarm_item_views_tree" model="ir.ui.view">
        <field name="name">roke.abnormal.alarm.item.tree</field>
        <field name="model">roke.abnormal.alarm.item</field>
        <field name="arch" type="xml">
            <tree>
                <field name="code"/>
                <field name="name"/>
                <field name="type_id"/>
                <field name="department_id"/>
                <field name="employee_ids" widget="many2many_tags"/>
                <field name="note"/>
            </tree>
        </field>
    </record>

    <record id="roke_abnormal_alarm_item_views_form" model="ir.ui.view">
        <field name="name">roke.abnormal.alarm.item.form</field>
        <field name="model">roke.abnormal.alarm.item</field>
        <field name="arch" type="xml">
            <form>
                <group col="4">
                    <group>
                        <field name="code" readonly="1"/>
                        <field name="department_id" options="{'no_create': True}"/>
                    </group>
                    <group>
                        <field name="name"/>
                        <field name="employee_ids" widget="many2many_tags" options="{'no_create': True}"/>
                    </group>
                    <group>
                        <field name="type_id" required="1"/>
                    </group>
                    <group>
                        <field name="note"/>
                        <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                    </group>
                </group>
            </form>
        </field>
    </record>

    <record id="roke_abnormal_alarm_item_views_action" model="ir.actions.act_window">
        <field name="name">异常项目</field>
        <field name="res_model">roke.abnormal.alarm.item</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
    </record>
</odoo>