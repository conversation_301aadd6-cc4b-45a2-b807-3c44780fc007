<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="view_wylj_roke_stock_other_in_action" model="ir.actions.act_window">
        <field name="name">报废入库单</field>
        <field name="res_model">roke.mes.stock.picking</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="search_view_id" ref="roke_mes_stock.view_roke_mes_stock_picking_search"/>
        <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('roke_mes_stock.view_roke_mes_stock_general_in_tree')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('roke_mes_stock.view_roke_mes_general_in_form')})]"/>
        <field name="domain">[("picking_logotype","=","BFPRK")]
        </field>
        <field name="context">{'picking_logotype':'BFPRK'}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                点击左上角“创建”按钮新增一个报废入库单。
            </p>
        </field>
    </record>
    <menuitem id="roke_wylj_stock_stock_other_in_picking_menu" parent="roke_mes_stock.roke_mes_stock_in_picking_menu"
              sequence="30" name="报废入库单" action="view_wylj_roke_stock_other_in_action"
              groups="base.group_system"/>

    <record id="inherit_wylj_view_roke_scrap_order_form_view" model="ir.ui.view">
        <field name="name">inherit.scrap.order.form</field>
        <field name="model">roke.scrap.order</field>
        <field name="inherit_id" ref="roke_mes_quality.view_roke_scrap_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='multi_clean_scrap']" position="after">
                <button name="action_in_stock_move" type="object" class="oe_highlight"
                        string="报废入库">
                </button>
            </xpath>
            <xpath expr="//field[@name='employee_ids']" position="after">
                <field name="container_code"/>
            </xpath>
        </field>
    </record>
</odoo>