<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="view_roke_document_inherit_production_task_form" model="ir.ui.view">
        <field name="name">roke.document.inherit.production.task.form</field>
        <field name="model">roke.production.task</field>
        <field name="inherit_id" ref="roke_mes_production.view_roke_production_task_form"/>
        <field name="arch" type="xml">
            <xpath expr="//form" position="replace">
                <form string="生产任务">
                    <header>
                        <button name="create_work_order" type="object" string="生成工单"
                                attrs="{'invisible': ['|', ('work_order_ids', '!=', []), ('state', '=', '暂停')]}"
                                class="oe_highlight"/>
                        <button name="action_split_task" type="object" string="拆分任务" class="btn btn-warning"
                                attrs="{'invisible': ['|', ('record_ids', '!=', []), ('state', '=', '暂停')]}"/>
                        <button name="force_finish" type="object" string="强制完工" class="btn btn-danger"
                                attrs="{'invisible': [('state', '!=', '未完工')]}" confirm="确认强制完工？"/>
                        <button name="cancel_force_finish" type="object" string="取消强制完工" class="btn btn-danger"
                                attrs="{'invisible': [('state', '!=', '强制完工')]}" confirm="确认取消强制完工？"/>
                        <button name="make_suspend" type="object" string="暂停" class="btn btn-danger"
                                attrs="{'invisible': [('state', '!=', '未完工')]}" confirm="确认暂停？"
                        />
                        <button name="cancel_make_suspend" type="object" string="取消暂停" class="btn btn-danger"
                                attrs="{'invisible': [('state', '!=', '暂停')]}" confirm="确认取消暂停？"
                        />
                        <field name="state" widget="statusbar" statusbar_visible="未完工,已完工"/>
                    </header>
                    <div name="button_box" class="oe_button_box">
                        <button name="pt_action_work_order" type="object" class="oe_stat_button" icon="fa-file">
                            <field name="wo_qty" widget="statinfo" string="查看工单"/>
                        </button>
                    </div>
                    <field name="allow_create_wo" invisible="1"/>
                    <field name="record_ids" invisible="1"/>
                    <group id="g1" col="4">
                        <group>
                            <field name="product_id" options="{'no_create': True}"
                                   attrs="{'readonly': [('allow_create_wo', '!=', True)]}"/>
                            <label for="plan_qty"/>
                            <div name="plan_qty" class="o_row">
                                <field name="plan_qty" required="1"
                                       attrs="{'readonly': ['|',('allow_create_wo', '!=', True),('state', '=', '已完工')]}"/>
                                <span name="plan_uom">
                                    <field name="uom_id"/>
                                </span>
                            </div>
                            <field name="customer_id" attrs="{'readonly': [('state', '=', '已完工')]}"
                                   domain="[('customer', '=', True)]"/>
                            <field name="workshop_id" options="{'no_create': True}"/>
                            <field name="plan_start_date" required="1" attrs="{'readonly': [('state', '=', '已完工')]}"/>
                            <field name="mold_id"/>
                            <field name="create_date" string="创建人"/>
                        </group>
                        <group>
                            <field name="routing_id" options="{'no_create': True}"
                                   attrs="{'readonly': [('allow_create_wo', '!=', True)]}"/>
                            <field name="finish_qty" readonly="1"/>
                            <field name="department_id" options="{'no_create': True}"/>
                            <field name="team_id" options="{'no_create': True}"/>
                            <field name="plan_date" required="1" attrs="{'readonly': [('state', '=', '已完工')]}"/>
                            <field name="tool_id"/>
                            <field name="create_date" string="创建时间"/>

                        </group>
                        <group>
                            <!--产品BOM-->
                            <field name="e_bom_id" options="{'no_create': True}"
                                   domain="[('product_id', '=', product_id)]"
                                   attrs="{'readonly': [('allow_create_wo', '!=', True)]}"/>
                            <field name="qualified_qty"/>
                            <field name="classes_id"/>
                            <field name="priority" attrs="{'readonly': [('state', '=', '已完工')]}"/>
                            <field name="start_date" readonly="1"/>
                            <field name="equipment_id"/>
                            <field name="pass_rate" readonly="1"/>
                        </group>
                        <group>
                            <!--单件批次号-->
                            <field name="lot_code"/>
                            <field name="sum_unqualified_qty" readonly="1"/>
                            <field name="work_center_id" options="{'no_create': True}"/>
                            <field name="type" required="1"/>
                            <field name="finish_date" readonly="1"/>
                            <field name="equipment_wight"/>
                        </group>
                    </group>
                    <notebook>
                        <page name="work_order_ids" string="工单">
                            <field name="has_child_process" invisible="1"/>
                            <button name="multi_add_process_action" string="批量添加工序" type="object"
                                    class="oe_highlight"
                                    attrs="{'invisible': ['|', '|', '|', ('allow_create_wo', '!=', True), ('product_id', '=', False), ('plan_qty', '=', 0), ('has_child_process', '=', True)]}"/>
                            <div name="message" class="alert alert-info" role="alert" style="margin-bottom:0px;"
                                 attrs="{'invisible': ['|', ('has_child_process', '!=', True), ('work_order_ids', '!=', [])]}">
                                当前工艺路线存在子工序，请保存后点击【生成工单】按钮。
                            </div>
                            <field name="work_order_ids"
                                   context="{'tree_view_ref': 'roke_mes_production.view_roke_work_order_edit_tree'}"/>
                        </page>
                        <page name="pt_demand_ids" string="下级物料/半成品">
                            <field name="can_multi_produce" invisible="1"/>
                            <button type="object" name="refresh_material_demand" icon="fa-refresh" string="刷新"
                                    attrs="{'invisible': [('child_pt_count', '!=', 0)]}"/>
                            <button type="object" name="check_all_child_product" class="oe_highlight"
                                    string="所有下级物料"/>
                            <button type="object" name="action_multi_create_material_task" icon="fa-wrench"
                                    class="oe_highlight" string="批量生产"
                                    attrs="{'invisible': [('can_multi_produce', '!=', True)]}"/>

                            <button name="action_child_production_task" type="object" class="oe_stat_button"
                                    icon="fa-tasks" attrs="{'invisible': [('child_pt_count', '=', 0)]}">
                                <field name="child_pt_count" widget="statinfo" string="下级任务"/>
                            </button>
                            <field name="pt_demand_ids" readonly="1">
                                <tree decoration-muted="not first">
                                    <field name="pt_id" invisible="1"/>
                                    <field name="can_produce" invisible="1"/>
                                    <field name="first" invisible="1"/>
                                    <field name="max_allow_qty" invisible="1"/>
                                    <field name="product_id"/>
                                    <field name="material_id"/>
                                    <field name="demand_qty"/>
                                    <field name="in_production_qty"/>
                                    <field name="defect_qty" decoration-danger="defect_qty &gt; 0"
                                           decoration-success="defect_qty &lt;= 0"/>
                                    <button name="action_create_material_task" type="object" string="生产"
                                            class="oe_highlight"
                                            attrs="{'invisible': ['|',('can_produce', '!=', True),('max_allow_qty', '&lt;=', 0)]}"/>
                                </tree>
                            </field>
                        </page>
                        <page name="child_pt_ids" string="下级任务">
                            <field name="child_pt_ids">
                                <tree editable="bottom">
                                    <field name="code" string="子任务编号" readonly="1"/>
                                    <field name="product_id" options="{'no_create': True}"/>
                                    <field name="product_specification"/>
                                    <field name="product_model"/>
                                    <field name="routing_id" options="{'no_create': True}"/>
                                    <field name="plan_date"/>
                                    <field name="plan_qty"/>
                                    <field name="finish_qty" readonly="1"/>
                                    <field name="sum_unqualified_qty"/>
                                    <field name="plan_work_hours"/>
                                    <field name="work_hours"/>
                                    <field name="project_code"/>
                                </tree>
                            </field>
                        </page>
                        <page string="作业规范/指导" name="standard_item_ids">
                            <group string="作业规范">
                                <field name="standard_item_ids" nolabel="1"
                                       context="{'tree_view_ref': 'roke_mes_base.view_roke_work_standard_item_editable_tree'}"/>
                            </group>
                            <group string="作业指导" name="attachment">
                                <field name="instruction_file_data" readonly="False" widget='image'
                                       options='{"zoom": true, "preview_image":"image_128"}'/>
                            </group>
                        </page>
                        <page string="图号附件">
                            <field name="attachment_ids" widget="many2many_binary"/>
                        </page>
                        <page string="设备点检记录">
                            <field name="check_item_ids">
                                <tree editable="bottom">
                                    <field name="equipment_id"/>
                                    <field name="inspection_plan_id"/>
                                    <field name="employee_id"/>
                                    <field name="check_time"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                    <group id="g2">
                        <field name="note" placeholder="此处可以填写备注或描述"/>
                    </group>
                    <group id="g3" col="8">
                        <field name="order_id" readonly="1"/>
                        <field name="project_code"/>
                        <field name="create_uid" string="创建人"/>
                        <field name="create_date" string="创建时间"/>
                    </group>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </xpath>
        </field>
    </record>
</odoo>