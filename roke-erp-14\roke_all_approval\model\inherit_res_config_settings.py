# -*- coding: utf-8 -*-
"""
Description:
    系统参数
Versions:
    Created by www.inspur.com
"""
from odoo import models, fields, api
from odoo.exceptions import ValidationError

class InheritConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    module_roke_approval_salary = fields.Boolean("工资管理-相关单据")

    @api.constrains('module_roke_approval_salary')
    def _check_module_roke_approval_salary(self):
        for rec in self:
            salary = self.env['ir.module.module'].search([('name','=','roke_mes_salary')],limit=1)
            if salary.state != 'installed' and rec.module_roke_approval_salary:
                raise ValidationError('未安装工资模块,无法配置工资相关审批!')

    module_roke_approval_sale = fields.Boolean("销售管理-相关单据")

    @api.constrains('module_roke_approval_sale')
    def _check_module_roke_approval_sale(self):
        for rec in self:
            sale = self.env['ir.module.module'].search([('name', '=', 'roke_mes_sale')], limit=1)
            if sale.state != 'installed' and rec.module_roke_approval_sale:
                raise ValidationError('未安装销售模块,无法配置销售相关审批!')

    module_roke_approval_purchase = fields.Boolean("采购管理-相关单据")

    @api.constrains('module_roke_approval_purchase')
    def _check_module_roke_approval_purchase(self):
        for rec in self:
            purchase = self.env['ir.module.module'].search([('name', '=', 'roke_mes_purchase')], limit=1)
            if purchase.state != 'installed' and rec.module_roke_approval_purchase:
                raise ValidationError('未安装采购模块,无法配置采购相关审批!')

    module_roke_approval_account = fields.Boolean("应收应付-相关单据")

    @api.constrains('module_roke_approval_account')
    def _check_module_roke_approval_account(self):
        for rec in self:
            account = self.env['ir.module.module'].search([('name', '=', 'roke_mes_account')], limit=1)
            if account.state != 'installed' and rec.module_roke_approval_account:
                raise ValidationError('未安装应收应付模块,无法配置应收应付相关审批!')

    module_roke_approval_expense = fields.Boolean("费用管理-相关单据")

    @api.constrains('module_roke_approval_expense')
    def _check_module_roke_approval_expense(self):
        for rec in self:
            expense = self.env['ir.module.module'].search([('name', '=', 'roke_mes_expense')], limit=1)
            if expense.state != 'installed' and rec.module_roke_approval_expense:
                raise ValidationError('未安装费用模块,无法配置费用相关审批!')

    module_roke_approval_production = fields.Boolean("生产管理-相关单据")

    @api.constrains('module_roke_approval_production')
    def _check_module_roke_approval_production(self):
        for rec in self:
            production = self.env['ir.module.module'].search([('name', '=', 'roke_mes_production')], limit=1)
            if production.state != 'installed' and rec.module_roke_approval_production:
                raise ValidationError('未安装生产管理,无法配置生产相关审批!')

    module_roke_approval_stock = fields.Boolean("仓库管理-相关单据")

    @api.constrains('module_roke_approval_stock')
    def _check_module_roke_approval_stock(self):
        for rec in self:
            stock = self.env['ir.module.module'].search([('name', '=', 'roke_mes_stock')], limit=1)
            if stock.state != 'installed' and rec.module_roke_approval_stock:
                raise ValidationError('未安装仓库模块,无法配置仓库相关审批!')

    module_roke_approval_account_sale = fields.Boolean("销售支票-相关单据")

    @api.constrains('module_roke_approval_account_sale')
    def _check_module_roke_approval_account_sale(self):
        for rec in self:
            account_sale = self.env['ir.module.module'].search([('name', '=', 'roke_mes_account_sale')], limit=1)
            if account_sale.state != 'installed' and rec.module_roke_approval_account_sale:
                raise ValidationError('未安装销售支票模块,无法配置销售支票相关审批!')

    module_roke_approval_account_purchase = fields.Boolean("采购支票-相关单据")

    @api.constrains('module_roke_approval_account_purchase')
    def _check_module_roke_approval_account_purchase(self):
        for rec in self:
            account_purchase = self.env['ir.module.module'].search([('name', '=', 'roke_mes_account_purchase')], limit=1)
            if account_purchase.state != 'installed' and rec.module_roke_approval_account_purchase:
                raise ValidationError('未安装采购支票模块,无法配置采购支票相关审批!')

    module_roke_approval_workshop = fields.Boolean("车间物料-相关单据")

    @api.constrains('module_roke_approval_workshop')
    def _check_module_roke_approval_workshop(self):
        for rec in self:
            workshop = self.env['ir.module.module'].search([('name', '=', 'roke_mes_workshop_material')], limit=1)
            if workshop.state != 'installed' and rec.module_roke_approval_workshop:
                raise ValidationError('未安装车间物料模块,无法配置车间物料相关审批!')

    module_roke_approval_market = fields.Boolean("营销管理-相关单据")

    @api.constrains('module_roke_approval_market')
    def _check_module_roke_approval_market(self):
        for rec in self:
            market = self.env['ir.module.module'].search([('name', '=', 'roke_sale_market')], limit=1)
            if market.state != 'installed' and rec.module_roke_approval_market:
                raise ValidationError('未安装营销管理模块,无法配置营销相关审批!')
    
    module_roke_approval_documents = fields.Boolean("文档管理-相关单据")

    @api.constrains('module_roke_approval_documents')
    def _check_module_roke_approval_documents(self):
        for rec in self:
            documents = self.env['ir.module.module'].search([('name', '=', 'roke_mes_documents')], limit=1)
            if documents.state != 'installed' and rec.module_roke_approval_documents:
                raise ValidationError('未安装文档管理模块,无法配置文档相关审批!')

    module_roke_approval_mold = fields.Boolean("模具管理-相关单据")

    @api.constrains('module_roke_approval_mold')
    def _check_module_roke_approval_mold(self):
        for rec in self:
            mold = self.env['ir.module.module'].search([('name', '=', 'roke_mes_sjzzh')], limit=1)
            if mold.state != 'installed' and rec.module_roke_approval_mold:
                raise ValidationError('未安装石家庄中汇扩展模块,无法配置文档相关审批!')