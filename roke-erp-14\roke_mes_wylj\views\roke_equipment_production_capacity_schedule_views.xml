<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Tree View -->
    <record id="view_roke_equipment_production_capacity_schedule_tree" model="ir.ui.view">
        <field name="name">roke.equipment.production.capacity.schedule.tree</field>
        <field name="model">roke.equipment.production.capacity.schedule</field>
        <field name="arch" type="xml">
            <tree string="设备产能表">
                <field name="equipment_id"/>
                <field name="line_ids" widget="many2many_tags"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_roke_equipment_production_capacity_schedule_form" model="ir.ui.view">
        <field name="name">roke.equipment.production.capacity.schedule.form</field>
        <field name="model">roke.equipment.production.capacity.schedule</field>
        <field name="arch" type="xml">
            <form string="设备产能表">
                <group>
                    <field name="equipment_id"/>
                    <field name="note"/>
                </group>
                <notebook>
                    <page string="产能明细">
                        <field name="line_ids" mode="tree,form">
                            <tree editable="bottom">
                                <field name="product_id"/>
                                <field name="product_code"/>
                                <field name="product_features"/>
                                <field name="product_category"/>
                                <field name="production_per_mold"/>
                                <field name="equipment_capacity"/>
                                <field name="mold_id"/>
                                <field name="tool_id"/>
                                <field name="scrap_rate"/>
                                <field name="loss_rate"/>
                                <field name="safety_stock"/>
                                <field name="max_debug_times"/>
                            </tree>
                        </field>
                    </page>
                </notebook>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_roke_equipment_production_capacity_schedule_search" model="ir.ui.view">
        <field name="name">roke.equipment.production.capacity.schedule.search</field>
        <field name="model">roke.equipment.production.capacity.schedule</field>
        <field name="arch" type="xml">
            <search string="设备产能表搜索">
                <field name="equipment_id"/>
                <filter string="按设备" name="group_by_equipment" context="{'group_by':'equipment_id'}"/>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_roke_equipment_production_capacity_schedule" model="ir.actions.act_window">
        <field name="name">设备产能表</field>
        <field name="res_model">roke.equipment.production.capacity.schedule</field>
        <field name="view_mode">tree,form</field>
<!--        <field name="target">new</field>-->
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_roke_equipment_production_capacity_schedule"
              name="设备产能表"
              parent="roke_mes_base.roke_base_production_menu"
              action="action_roke_equipment_production_capacity_schedule"
              sequence="10"/>
</odoo>
