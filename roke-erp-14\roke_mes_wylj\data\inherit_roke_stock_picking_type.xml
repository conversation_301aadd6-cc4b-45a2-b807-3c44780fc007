<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data noupdate="1">
        <record id="wylj_stock_picking_type_order_in" model="roke.mes.stock.picking.type">
            <field name="name">报废品入库</field>
            <field name="index">BF/IN/</field>
            <field name="type">入库</field>
            <field name="picking_logotype">BFPRK</field>
            <field name="src_location_id" ref="roke_mes_stock.stock_location_virtual"/>
            <field name="dest_location_id" ref="roke_mes_stock.stock_location_locations"/>
        </record>
    </data>
</odoo>