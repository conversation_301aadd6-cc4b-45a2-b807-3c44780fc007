<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_roke_wizard_multi_add_process_form" model="ir.ui.view">
        <field name="name">add.collection.inspection.plan.form</field>
        <field name="type">form</field>
        <field name="model">add.collection.inspection.plan</field>
        <field name="priority" eval="20"/>
        <field name="arch" type="xml">
            <form>
                <group>
                    <group>
                        <field name="id" invisible="1"/>
                        <field name="equipment_ids" widget="many2many_tags" options="{'no_create_edit': True}"/>
                    </group>
                </group>
                <field name="line_ids" widget="multi_select_tree" >
                    <tree editable="bottom">
                        <field name="wizard_id" invisible="1"/>
                        <field name="line_id" force="1" required="1" />
                        <field name="item_id"/>
                    </tree>
                </field>
                <footer>
                    <button name='confirm' string='确认' type='object' class='oe_highlight'/>
                    <button string="取消" class="oe_link" special="cancel" />
                </footer>
            </form>
        </field>
    </record>
</odoo>
