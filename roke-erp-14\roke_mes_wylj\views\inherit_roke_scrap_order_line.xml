<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="view_inherit_roke_scrap_order_form" model="ir.ui.view">
        <field name="name">inherit.roke.scrap.order.form</field>
        <field name="model">roke.scrap.order</field>
        <field name="inherit_id" ref="roke_mes_quality.view_roke_scrap_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='line_ids']" position="replace">
                <field name="line_ids" attrs="{'readonly': [('state', '!=', '待确认')]}">
                    <tree string="报废明细" editable="bottom">
                        <field name="order_id" optional="hide" readonly="1"/>
                        <field name="wr_id" optional="show" readonly="1"/>
                        <field name="product_id" optional="show" readonly="1"/>
                        <field name="reason_id" optional="show"/>
                        <field name="qty" optional="show" sum="报废合计"/>
                        <field name="clean_qty" optional="show" sum="已处理数量" readonly="1"/>
                        <field name="replenish_qty" optional="show" sum="已补件数量" readonly="1"/>
                        <field name="replenish_pt_ids" optional="show" readonly="1"/>
                        <field name="allow_replenish_qty" invisible="1"/>
                        <field name="state" invisible="1"/>
                        <field name="note" optional="show"/>
                        <field name="employee_id"/>
                        <field name="date"/>
                        <button name="execute_replenish" type="object" string="执行补件" class="oe_highlight"
                                attrs="{'invisible': ['|', ('allow_replenish_qty', '&lt;=', 0), ('state', 'in', ['取消', '待确认'])]}"/>
                    </tree>
                </field>
            </xpath>
        </field>
    </record>
</odoo>