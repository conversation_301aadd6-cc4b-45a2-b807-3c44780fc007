from odoo import models, fields, api, _
from odoo.exceptions import ValidationError

class RokeContainerRecord(models.Model):
    _name = 'roke.container.record'
    _description = '容器记录'
    _order = 'id desc'

    code = fields.Char(string='容器编号')
    finish_qty = fields.Float(string='完成数量')
    finish_time = fields.Datetime(string='完成时间', default=fields.Datetime.now())
    work_order_id = fields.Many2one('roke.work.order', string='生产工单')
    task_id = fields.Many2one('roke.production.task', string='生产任务')