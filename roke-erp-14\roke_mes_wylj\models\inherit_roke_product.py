from odoo import api, fields, models
from odoo.exceptions import UserError, ValidationError
import xlwt
import xlrd
from io import BytesIO
import base64
from odoo.osv import expression

class InheritWyljEBomLine(models.Model):
    _inherit = "roke.mes.e_bom.line"
    sequence = fields.Integer(string="顺序号")

class InheritRokeProduct(models.Model):
    _inherit = 'roke.product'

    atrol_scheme_id = fields.Many2one("roke.quality.scheme", string="巡检方案")
    arrival_scheme_id = fields.Many2one("roke.quality.scheme", string="到货检方案")
    shipment_scheme_id = fields.Many2one("roke.quality.scheme", string="发货检方案")
    collection_standard_ids = fields.One2many("roke.data.collection.standard", "product_id", string="数据采集标准")

    #plm 接口 字段
    eversion = fields.Integer(string="版本号")
    description = fields.Char(string="描述")
    type = fields.Selection(selection_add=[("专利", "专利"),("模具","模具"),("工装","工装")])
    origin = fields.Char(string="来源")
    first_product_code = fields.Char(string="首用产品代号")
    universal = fields.Char(string="通用化")
    material = fields.Char(string="材料")
    provider = fields.Char(string="供应商")
    montage = fields.Char(string="所属装配")
    material_type = fields.Char(string="毛坯种类")
    color_item = fields.Boolean(string="是否颜色件")
    dwg_no = fields.Char(string="图号")
    standard_no = fields.Char(string="标准号")
    specification_model = fields.Char(string="规格型号")
    surfacetreatment = fields.Char(string='表面处理')
    istooling = fields.Char(string='是否工装')
    material_type2 = fields.Char(string='材料类型')
    wlfl = fields.Char(string='物料分类')
    khmc = fields.Char(string='客户名称')
    khljh = fields.Char(string='客户零件号')
    zhuanshenqinghao = fields.Char(string='专利申请号')
    zhuanliha = fields.Char(string='专利号')
    zhuanlishenqingri = fields.Date(string='专利申请日')
    zhuanlishouquanri = fields.Date(string='专利授权日')
    shouquangonggaori = fields.Date(string='授权公告日')
    zhuanliyouxiaoqi = fields.Char(string='专利有效期')
    zhuanlifamingren = fields.Char(string='专利发明人')
    zhuanlidailigongsi = fields.Char(string='专利代理公司')
    newitem1 = fields.Char(string='专利代理人')
    zhuanlileixing = fields.Char(string='专利类型')
    zhuanliquanren = fields.Char(string='专利权人')
    dizhi = fields.Char(string='地址')
    team_id = fields.Char(string='生产班组')

    def action_import_standard(self):
        """打开上传数采标准向导"""
        res_id = self.env["roke.product.item.standard.import.wizard"].create({'product_id': self.id})

        return {
            'name': '导入数据采集标准',
            'type': 'ir.actions.act_window',
            'res_model': 'roke.product.item.standard.import.wizard',
            'view_mode': 'form',
            'target': 'new',
            'res_id': res_id.id,
            'view_id': self.env.ref('roke_mes_wylj.roke_product_item_standard_import_wizard_form').id,
        }


class RokeDataCollectionStandard(models.Model):
    _name = 'roke.data.collection.standard'
    _description = '数据采集标准'

    product_id = fields.Many2one('roke.product', string='产品')
    equipment_id = fields.Many2one('roke.mes.equipment', string='设备')
    check_item = fields.Char(string='检查项')
    check_item_id = fields.Many2one('roke.mes.equipment.item', string='数采检查项')
    standard_value = fields.Char(string='标准值')
    lower_limit = fields.Float(string='数据下限', digits=(16, 4))
    upper_limit = fields.Float(string='数据上限', digits=(16, 4))
    active = fields.Boolean(string='启用', default=True)

class RokeProductItemStandardImportWizard(models.TransientModel):
    _name = "roke.product.item.standard.import.wizard"
    _description = "导入数据采集标准"

    excel_file = fields.Binary(string='上传Excel文件')
    product_id = fields.Many2one('roke.product', string='产品')

    def generate_excel(self):
        """
            导出模板样式
        """
        result = [
            ['设备', '数采检查项', '标准值', '数据上限', '数据下限'],
            ['cs0001', '6489284546', '100.00', '200.00', '50.00'],
        ]
        wbk = xlwt.Workbook()
        sheet = wbk.add_sheet('Sheet1', cell_overwrite_ok=True)
        for i in range(len(result)):
            for j in range(len(result[i])):
                sheet.write(i, j, result[i][j])
        buffer = BytesIO()
        wbk.save(buffer)
        data = base64.encodebytes(buffer.getvalue())
        return data

    def action_export_data(self):
        # 调用自定义excel模板
        res = self.env["export.excel.template"].create({'file': self.generate_excel()})
        excel_url = '/web/content?model=%s&id=%s&field=file&download=true&filename=%s.xls' % (
        "export.excel.template", res.id, "导出模板")

        value = dict(
            type='ir.actions.act_url',
            target='self',
            url=excel_url
        )
        return value

    def import_collection_standard(self):
        self.ensure_one()
        # 获取上传的文件
        excel_file = self.excel_file
        if not excel_file:
            raise UserError("请先上传Excel文件")

        try:
            # 解析Excel文件
            file_content = base64.b64decode(excel_file)
            workbook = xlrd.open_workbook(file_contents=file_content)
            sheet = workbook.sheet_by_index(0)

            # 清空现有数据
            self.product_id.collection_standard_ids.unlink()

            # 从第二行开始读取数据（假设第一行是标题）
            for row in range(1, sheet.nrows):
                vals = {
                    'product_id': self.product_id.id,
                    'equipment_id': self._get_equipment_id(sheet.cell_value(row, 0)),
                    'check_item_id': self._get_check_item_id(sheet.cell_value(row, 1)),
                    'standard_value': sheet.cell_value(row, 2),
                    'lower_limit': sheet.cell_value(row, 3),
                    'upper_limit': sheet.cell_value(row, 4),
                    'active': True
                }
                self.env['roke.data.collection.standard'].create(vals)

            self.excel_file = None

            return {
                'type': 'ir.actions.act_window_close'
            }

        except Exception as e:
            raise UserError(f"导入失败: {str(e)}")

    def _get_equipment_id(self, equipment_code):
        """根据设备编码获取设备ID"""
        equipment = self.env['roke.mes.equipment'].search([('code', '=', equipment_code)], limit=1)
        if not equipment:
            raise UserError(f"设备编码{equipment_code}不存在")
        return equipment.id

    def _get_check_item_id(self, check_item_code):
        """根据检查项编码获取检查项ID"""
        check_item = self.env['roke.mes.equipment.item'].search([('item_id', '=', check_item_code)], limit=1)
        if not check_item:
            raise UserError(f"检查项编码{check_item_code}不存在")
        return check_item.id



