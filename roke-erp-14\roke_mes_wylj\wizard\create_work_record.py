from odoo import api, fields, models
from odoo.exceptions import ValidationError


class QualityInheritCreateWRWizard(models.TransientModel):
    _inherit = "roke.create.work.record.wizard"

    def create_other_order(self, new_record):
        """
        创建报废单、返修单
        :return:
        """
        res = super(QualityInheritCreateWRWizard, self).create_other_order(new_record)
        if self.scrap_qty:
            details = []
            if not self.scrap_detail_ids:
                default_scrap_reason = self.env["roke.scrap.reason"].search([], limit=1)
                data = {
                    "reason_id": default_scrap_reason.id,
                    "qty": self.scrap_qty,
                    "note": "=====系统自动选择报废原因====="
                }
                if self.is_install_module():
                    data.update({
                        "reason_data": default_scrap_reason.name or ''
                    })
                details.append((0, 0, data))
            for detail in self.scrap_detail_ids:
                data = {
                    "reason_id": detail.reason_id.id,
                    "qty": detail.qty,
                    "note": detail.note,
                    "employee_id": detail.employee_id.id,
                    "date": detail.date,
                }
                if self.is_install_module():
                    data.update({
                        "reason_data": detail.reason_data or ''
                    })
                details.append((0, 0, data))
            self.env["roke.scrap.order"].create({
                "wr_id": new_record.id,
                "total": self.scrap_qty,
                "line_ids": details
            })
        if self.repair_qty:
            details = []
            if not self.repair_detail_ids:
                default_repair_reason = self.env["roke.repair.reason"].search([], limit=1)
                details.append((0, 0, {
                    "reason_id": default_repair_reason.id,
                    "qty": self.repair_qty,
                    "note": "=====系统自动选择返修原因====="
                }))
            for detail in self.repair_detail_ids:
                details.append((0, 0, {
                    "reason_id": detail.reason_id.id,
                    "qty": detail.qty,
                    "note": detail.note
                }))
            self.env["roke.repair.order"].create({
                "wr_id": new_record.id,
                "total": self.repair_qty,
                "line_ids": details
            }).confirm()
        return res


