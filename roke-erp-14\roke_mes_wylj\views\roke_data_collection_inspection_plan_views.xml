<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- 列表视图 -->
    <record id="view_data_collection_inspection_plan_tree" model="ir.ui.view">
        <field name="name">roke.data.collection.inspection.plan.tree</field>
        <field name="model">roke.data.collection.inspection.plan</field>
        <field name="arch" type="xml">
            <tree>
                <field name="code" string="方案编号"/>
                <field name="equipment_id" string="设备"/>
                <field name="active" string="启用"/>
                <field name="name"/>
            </tree>
        </field>
    </record>

    <!-- 表单视图 -->
    <record id="view_data_collection_inspection_plan_form" model="ir.ui.view">
        <field name="name">roke.data.collection.inspection.plan.form</field>
        <field name="model">roke.data.collection.inspection.plan</field>
        <field name="arch" type="xml">
            <form>
                <group col="3">
                    <field name="code" readonly="1"/>
                    <field name="name" required="1"/>
                    <field name="equipment_id"/>
                    <field name="note"/>
                    <field name="active"/>
                </group>
                <notebook>
                    <page string="数采点检项">
                        <button name="multi_add_inspection_action" string="批量添加点检项" type="object" class="oe_highlight" />
                        <field name="line_ids">
                            <tree editable="bottom">
                                <field name="check_item" required="1" domain="[('equipment_id', '=', parent.equipment_id)]"/>
                                <field name="item_id"/>
                            </tree>
                        </field>
                    </page>
                </notebook>
            </form>
        </field>
    </record>

    <!-- 动作和菜单 -->
    <record id="action_data_collection_inspection_plan" model="ir.actions.act_window">
        <field name="name">数采点检方案</field>
        <field name="res_model">roke.data.collection.inspection.plan</field>
        <field name="view_mode">tree,form</field>
    </record>

    <menuitem id="menu_data_collection_inspection_plan"
              name="数采点检方案"
              parent="roke_mes_equipment.roke_mes_equipment_manage_menu"
              sequence="10"
              action="action_data_collection_inspection_plan"/>
</odoo>