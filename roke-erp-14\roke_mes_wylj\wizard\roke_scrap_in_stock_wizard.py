from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class RokeScrapInStockWizard(models.TransientModel):
    _name = "roke.scrap.in.stock.wizard"

    material_id = fields.Many2one("roke.product", string="报废物料")
    qty = fields.Float(string="数量")
    dest_location_id = fields.Many2one("roke.mes.stock.location", string="入库仓库")
    line_ids = fields.One2many("roke.scrap.in.stock.wizard.line", "wizard_id", string="明细")

    def confirm(self):
        """
        执行报废处理
        :return:
        """
        # 生成其它入库单
        picking_type = self.env["roke.mes.stock.picking.type"].search([("picking_logotype", "=", "BFPRK")], limit=1)

        # pick = self.env["roke.mes.stock.picking"].create({
        #     'picking_type_id': picking_type.id,
        #     'src_location_id': picking_type.src_location_id.id,
        #     'dest_location_id': self.dest_location_id.id,
        #     'origin': "、".join(list(set(self.line_ids.scrap_line_id.order_id.mapped("code")))),
        #     'picking_date': fields.Date.context_today(self),
        # })
        # move_vals = []
        # for line in self.line_ids:
        #     move_vals.append((0, 0, {
        #         'src_location_id': picking_type.src_location_id.id,
        #         "dest_location_id": self.dest_location_id.id,
        #         "product_id": line.material_id.id,
        #         "qty": line.qty,
        #         "move_date": fields.Date.context_today(self),
        #         "picking_id": pick.id,
        #     }))
        # move = self.env["roke.mes.stock.move"].create(move_vals)
        #
        # move_line_vals = []
        # for line in self.line_ids:
        #     move_line_vals.append((0, 0, {
        #         'src_location_id': picking_type.src_location_id.id,
        #         'dest_location_id': self.dest_location_id.id,
        #         "product_id": line.material_id.id,
        #         "qty": line.qty,
        #         "move_id": move.id,
        #         "picking_id": pick.id
        #     }))
        # move_line = self.env["roke.mes.stock.move.line"].create(move_vals)
        #
        # pick.make_finish()

        pick = self.env["roke.mes.stock.picking"].create({
            'picking_type_id': picking_type.id,
            'src_location_id': picking_type.src_location_id.id,
            'dest_location_id': self.dest_location_id.id,
            'origin': "、".join(list(set(self.line_ids.scrap_line_id.order_id.mapped("code")))),
            'picking_date': fields.Date.context_today(self),
            'move_line_ids': [(0, 0, {
                'src_location_id': picking_type.src_location_id.id,
                "dest_location_id": self.dest_location_id.id,
                "product_id": self.material_id.id,
                "qty": self.qty,
                "move_date": fields.Date.context_today(self),
                "line_ids": [(0, 0, {
                    'src_location_id': picking_type.src_location_id.id,
                    'dest_location_id': self.dest_location_id.id,
                    "product_id": self.material_id.id,
                    "qty": self.qty,
                })]
            })],
        })
        pick.make_finish()

        for line in self.line_ids:
            if line.qty <= 0:
                continue
            line.scrap_line_id.write({"clean_qty": round(line.scrap_line_id.clean_qty + line.qty,
                                                         self.env["decimal.precision"].precision_get("Production"))})
            if line.scrap_line_id.order_id.total <= sum(line.scrap_line_id.order_id.line_ids.mapped("clean_qty")):
                # 修改返修单状态为“返修中”
                line.scrap_line_id.order_id.write({"state": "处理完成"})
            elif line.scrap_line_id.order_id.state == "确认":
                line.scrap_line_id.order_id.write({"state": "部分处理"})
        return {
            'name': '报废品入库单',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'views': [[self.env.ref('roke_mes_stock.view_roke_mes_general_in_form').id, 'form']],
            'target': 'current',
            'res_model': 'roke.mes.stock.picking',
            'res_id': pick.id
        }


class RokeScrapInStockWizardLine(models.TransientModel):
    _name = "roke.scrap.in.stock.wizard.line"
    _description = "报废处理明细"
    wizard_id = fields.Many2one("roke.scrap.in.stock.wizard", string="报废处理单")
    qty = fields.Float(string="数量")
    scrap_line_id = fields.Many2one("roke.scrap.order.line", string="报废单明细")
    material_id = fields.Many2one("roke.product", related="scrap_line_id.product_id", string="物料", store=True)


class InheritRokeMesStockPickingType(models.Model):
    _inherit = "roke.mes.stock.picking.type"

    picking_logotype = fields.Selection([('CGRKD', '采购入库单'),
                                         ('XSCKD', '销售出库单'),
                                         ('SCLLD', '生产领料单'),
                                         ('SCRKD', '生产入库单'),
                                         ('QTRKD', '其他入库单'),
                                         ('QTCKD', '其他出库单'),
                                         ('KCDBD', '库存调拨单'),
                                         ('KCPDB', '库存盘点单'),
                                         ('WTLLD', '委外领料单'),
                                         ('WTRKD', '委外入库单'),
                                         ('BFPRK', '报废入库单'),
                                         ], string="业务标识")
