# -*- coding: utf-8 -*-
"""
Description:
    生产领料单
Versions:
    Created by www.inspur.com
"""
from odoo import models, fields, api, _
from odoo.exceptions import UserError

class RokeWorkOrderWizard(models.TransientModel):
    _name = "roke.work.order.wizard"
    _description = "生产领料单"

    alarm_id = fields.Many2one("roke.abnormal.alarm", string="告警记录")
    task_id = fields.Many2one("roke.production.task", string="生产任务", index=True, ondelete="cascade")
    code = fields.Char(string="编号", index=True, copy=False)
    state = fields.Selection([("未完工", "未完工"), ("已完工", "已完工"), ("强制完工", "强制完工")], required=True, default="未完工", string="状态")
    priority = fields.Selection([('正常', '正常'), ('紧急', '紧急'), ('非常紧急', '非常紧急')], string="优先级", required=True, default='正常')
    type = fields.Selection([("生产", "生产")], required=True, default="生产", string="类型")
    product_id = fields.Many2one("roke.product", string="成品", index=True, ondelete="restrict")
    uom_id = fields.Many2one("roke.uom", string="单位", related="product_id.uom_id")
    plan_qty = fields.Float(string="计划数量", digits='Production')
    finish_qty = fields.Float(string="完工数量", digits='Production')
    unqualified_qty = fields.Float(string="不合格数", digits='Production')
    work_hours = fields.Float(string="工时")
    routing_id = fields.Many2one("roke.routing", string="工艺路线", index=True)
    routing_line_id = fields.Many2one("roke.routing.line", string="工艺明细", index=True)
    sequence = fields.Integer(string="序号", default=1)
    process_id = fields.Many2one("roke.process", string="工序", index=True, ondelete="restrict")
    plan_date = fields.Date(string="计划完成日期", default=fields.Date.context_today)
    note = fields.Text(string="备注")
    order_id = fields.Many2one("roke.production.order", string="生产订单", related="task_id.order_id", store=True)
    project_code = fields.Char(string="项目号", related="task_id.project_code", store=True)
    customer_id = fields.Many2one("roke.partner", string="客户")
    team_id = fields.Many2one("roke.work.team", string="指派班组")
    employee_ids = fields.Many2many("roke.employee", string="指派人员")
    work_center_id = fields.Many2one("roke.work.center", string="指派工作中心")
    dispatch_time = fields.Datetime(string="派工时间")
    finish_time = fields.Datetime(string="报工时间")
    from_routing = fields.Boolean(string="来源于工艺", default=False)
    instruction_file_data = fields.Image('作业指导图片', related="process_id.instruction_file_data", store=True)
    image_128 = fields.Image('作业指导图片', related="instruction_file_data", max_width=256, max_height=256)
    allow_edit = fields.Boolean(string="允许编辑", store=True, default=True)


    def confirm(self):
        order_id = self.env['roke.work.order'].create({
            'alarm_id': self.alarm_id.id,
            'task_id': self.task_id.id,
            'code': self.code,
            'state': self.state,
            'priority': self.priority,
            'type': self.type,
            'product_id': self.product_id.id,
            'plan_qty': self.plan_qty,
            'finish_qty': self.finish_qty,
            'unqualified_qty': self.unqualified_qty,
            'work_hours': self.work_hours,
            'routing_id': self.routing_id.id,
            'routing_line_id': self.routing_line_id.id,
            'sequence': self.sequence,
            'process_id': self.process_id.id,
            'plan_date': self.plan_date,
            'note': self.note,
            'customer_id': self.customer_id.id,
            'team_id': self.team_id.id,
            'employee_ids': self.employee_ids.ids,
            'work_center_id': self.work_center_id.id,
            'dispatch_time': self.dispatch_time,
            'finish_time': self.finish_time,
            'from_routing': self.from_routing,
            'allow_edit': self.allow_edit
        })
        if not order_id:
            raise UserError('数据填写错误，数据创建失败!')
