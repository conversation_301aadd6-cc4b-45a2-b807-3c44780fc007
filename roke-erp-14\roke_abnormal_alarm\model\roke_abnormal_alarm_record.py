# -*- coding: utf-8 -*-
"""
Description:
    异常告警
Versions:
    Created by www.inspur.com
"""
from urllib import parse
from odoo import api, exceptions, fields, models


class RokeAbnormalAlarmType(models.Model):
    _name = "roke.abnormal.alarm.type"
    _description = "异常类型"
    _rec_name = 'name'

    name = fields.Char(string="名称", index=True, required=True)
    item_ids = fields.One2many("roke.abnormal.alarm.item", "type_id", string="异常项目")
    note = fields.Text(string="备注")
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)


class RokeAbnormalAlarmItem(models.Model):
    _name = "roke.abnormal.alarm.item"
    _description = "异常项目"
    _rec_name = 'name'

    type_id = fields.Many2one("roke.abnormal.alarm.type", string="归属类型")
    code = fields.Char(string="编号", required=True, index=True, copy=False, tracking=True, default="保存自动生成编号")
    name = fields.Char(string="项目名称", index=True, required=True)
    department_id = fields.Many2one("roke.department", string="处理部门")
    employee_ids = fields.Many2many("roke.employee", string="处理人")
    note = fields.Text(string="备注")
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)

    @api.model
    def create(self, vals):
        vals["code"] = self.env['ir.sequence'].next_by_code('roke.abnormal.alarm.item.code')
        return super(RokeAbnormalAlarmItem, self).create(vals)


class RokeAbnormalAlarmState(models.Model):
    _name = "roke.abnormal.alarm.state"
    _description = "处理状态"
    _rec_name = 'name'

    name = fields.Char(string="状态名称")


class RokeAbnormalAlarmRecord(models.Model):
    _name = "roke.abnormal.alarm"
    _description = "异常表单"
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _rec_name = 'code'

    @api.model
    def _default_state(self):
        State = self.env['roke.abnormal.alarm.state']
        return State.search([], limit=1)

    @api.model
    def _default_is_equipment(self):
        module = self.env['ir.module.module'].search([('name','=','roke_mes_equipment')])
        return module.state == 'installed'

    @api.model
    def _default_is_enterprise(self):
        module = self.env['ir.module.module'].search([('name','=','roke_mes_quality_enterprise')])
        if module.state == 'installed':
            return True
        else:
            return False

    @api.model
    def _default_is_quality(self):
        module = self.env['ir.module.module'].search([('name','=','roke_mes_quality')])
        if module.state == 'installed':
            return True
        else:
            return False

    @api.model
    def _default_is_stock(self):
        module = self.env['ir.module.module'].search([('name','=','roke_mes_stock')])
        if module.state == 'installed':
            return True
        else:
            return False

    code = fields.Char(string="编号", required=True, index=True, copy=False, tracking=True, default="保存自动生成编号")
    abnormal_id = fields.Many2one("roke.abnormal.alarm.type", string="异常类型")
    abnormal_item_ids = fields.Many2many("roke.abnormal.alarm.item", string="异常项目")
    sponsor = fields.Many2one("res.users",string="发起人",default=lambda s: s.env.uid)
    originating_time = fields.Datetime(string="发起时间", default=fields.Datetime.now)
    note = fields.Text(string="备注")
    abnormal_note = fields.Text(string="原因描述")
    processing_results  = fields.Text(string="处理结果")
    recipient = fields.Many2one("res.users", string="接受人")
    handle_employee_ids = fields.Many2many("roke.employee", string="处理人")
    handle_department_id = fields.Many2one("roke.department", string="处理部门")
    work_center = fields.Many2one("roke.work.center", string="工作中心")
    process = fields.Many2one("roke.process", string="工序")
    product = fields.Many2one("roke.product", string="产品")
    color = fields.Integer(string="颜色")
    priority = fields.Selection([('0', '正常'),('1', '普通'),('2', '加急'),('3', '危险')], '优先级' ,default='1')
    res_id = fields.Integer(string="记录id")
    res_model = fields.Char(string="记录模型")
    state_id = fields.Many2one('roke.abnormal.alarm.state',default=_default_state,group_expand='_group_expand_state_id',tracking=True)
    is_equipment = fields.Boolean(string='是否安装设备管理',default=_default_is_equipment)
    is_enterprise = fields.Boolean(string='是否安装质量管理标准版',default=_default_is_enterprise)
    is_quality = fields.Boolean(string='是否安装质量管理',default=_default_is_quality)
    is_stock = fields.Boolean(string='是否安装仓库管理',default=_default_is_stock)
    # 设备维修单
    maintenance_ids = fields.One2many("roke.mes.maintenance.order", "alarm_id", string="设备维修单")
    maintenance_count = fields.Integer(string="维修单数量", compute="_compute_maintenance_count")
    # 质检单
    quality_count = fields.Integer(string="质检单数量", compute="_compute_quality_count")
    image_ids = fields.Many2many("ir.attachment", "roke_abnormal_alarm_attachment_rel", string="相关附件")

    @api.model
    def create(self, vals):
        vals["code"] = self.env['ir.sequence'].next_by_code('roke.abnormal.alarm.code')
        return super(RokeAbnormalAlarmRecord, self).create(vals)

    @api.model
    def _group_expand_state_id(self, stages, domain, order):
        return stages.search([], order=order)

    def _compute_maintenance_count(self):
        for record in self:
            record.maintenance_count = len(record.maintenance_ids)

    def action_view_maintenance(self):
        result = self.env["ir.actions.actions"]._for_xml_id('roke_mes_equipment.view_roke_mes_repair_order_action')
        maintenance_ids = self.mapped('maintenance_ids')
        if not maintenance_ids or len(maintenance_ids) > 1:
            result['domain'] = "[('id','in',%s)]" % (maintenance_ids.ids)
        elif len(maintenance_ids) == 1:
            res = self.env.ref('roke_mes_equipment.view_roke_mes_repair_order_form', False)
            form_view = [(res and res.id or False, 'form')]
            if 'views' in result:
                result['views'] = form_view + [(state, view) for state, view in result['views'] if view != 'form']
            else:
                result['views'] = form_view
            result['res_id'] = maintenance_ids.id
        return result

    # # 报废单
    # scrap_ids = fields.One2many("roke.scrap.order", "alarm_id", string="报废单")
    # scrap_count = fields.Integer(string="报废单数量", compute="_compute_scrap_count_count")
    #
    # def _compute_scrap_count_count(self):
    #     for record in self:
    #         record.scrap_count = len(record.scrap_ids)
    #
    # def action_view_scrap(self):
    #     result = self.env["ir.actions.actions"]._for_xml_id('roke_mes_quality.view_roke_scrap_order_action')
    #     scrap_ids = self.mapped('scrap_ids')
    #     if not scrap_ids or len(scrap_ids) > 1:
    #         result['domain'] = "[('id','in',%s)]" % (scrap_ids.ids)
    #     elif len(scrap_ids) == 1:
    #         res = self.env.ref('roke_mes_quality.view_roke_scrap_order_form', False)
    #         form_view = [(res and res.id or False, 'form')]
    #         if 'views' in result:
    #             result['views'] = form_view + [(state, view) for state, view in result['views'] if view != 'form']
    #         else:
    #             result['views'] = form_view
    #         result['res_id'] = scrap_ids.id
    #     return result

    # # 生产领料单
    # order_ids = fields.One2many("roke.work.order", "alarm_id", string="生产领料单")
    # order_count = fields.Integer(string="生产领料单数量", compute="_compute_order_count")
    #
    # def _compute_order_count(self):
    #     for record in self:
    #         record.order_count = len(record.order_ids)
    #
    # def action_view_order(self):
    #     result = self.env["ir.actions.actions"]._for_xml_id('roke_mes_production.view_roke_work_order_action')
    #     order_ids = self.mapped('order_ids')
    #     if not order_ids or len(order_ids) > 1:
    #         result['domain'] = "[('id','in',%s)]" % (order_ids.ids)
    #     elif len(order_ids) == 1:
    #         res = self.env.ref('roke_mes_production.view_roke_work_order_form', False)
    #         form_view = [(res and res.id or False, 'form')]
    #         if 'views' in result:
    #             result['views'] = form_view + [(state, view) for state, view in result['views'] if view != 'form']
    #         else:
    #             result['views'] = form_view
    #         result['res_id'] = order_ids.id
    #     return result

    # 设备告警
    def maintenance_alarm(self):
        return {
            'name': '设备告警',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'target': 'new',
            'res_model': 'roke.maintenance.order.wizard',
            'context': {'default_alarm_id': self.id}
        }

    # 质量告警
    def quality_alarm(self):
        """
        生成质检单
        :return:
        """
        pass

    # 报废告警
    def scrap_alarm(self):
        pass
        # return {
        #     'name': '报废告警',
        #     'type': 'ir.actions.act_window',
        #     'view_mode': 'form',
        #     'target': 'new',
        #     'res_model': 'roke.scrap.order.wizard',
        #     'context': {'default_alarm_id': self.id}
        # }

    # 库存告警
    def stock_alarm(self):
        pass
        # return {
        #     'name': '库存告警',
        #     'type': 'ir.actions.act_window',
        #     'view_mode': 'form',
        #     'target': 'new',
        #     'res_model': 'roke.work.order.wizard',
        #     'context': {'default_alarm_id': self.id}
        # }

    def get_image_urls(self, file_type=None):
        """
        获取作业知道图片预览地址
        TODO 其他格式预览地址
        :param file_type: 入参‘base64’或预览地址
        :return:
        """
        base_url = self.sudo().env['ir.config_parameter'].get_param('web.base.url')
        attachments = self.image_ids
        res = []
        for attachment in attachments:
            if not attachment:
                continue
            if file_type == "base64" or attachment.mimetype == "video/mp4":
                res.append({
                    "name": attachment.name,
                    "type": "base64",
                    "data": attachment.datas,
                    "is_picture": False,
                })
                continue
            if not attachment.access_token:
                attachment.generate_access_token()
            if attachment.mimetype == "application/pdf":
                # pdf 预览
                content_url = parse.quote("/web/content/%s?access_token=%s" % (str(attachment.id), attachment.sudo().access_token))
                url = "%s/web/static/lib/pdfjs/web/viewer.html?file=%s" % (base_url, content_url)
                res.append({
                    "name": attachment.name,
                    "type": "url",
                    "data": url,
                    "is_picture": False,
                })
            else:
                # 图片 预览
                url = "%s/web/image/%s?access_token=%s" % (base_url, str(attachment.id), attachment.sudo().access_token)
                res.append({
                    "name": attachment.name,
                    "type": "url",
                    "data": url,
                    "is_picture": True if attachment.index_content == 'image' else False,
                })
        return res


