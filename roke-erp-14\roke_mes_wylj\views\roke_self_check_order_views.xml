<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Tree视图 -->
    <record id="view_self_check_order_tree" model="ir.ui.view">
        <field name="name">roke.self.check.order.tree</field>
        <field name="model">roke.self.check.order</field>
        <field name="arch" type="xml">
            <tree>
                <field name="task_id" string="生产任务"/>
                <field name="state" string="状态"/>
                <field name="work_order_id" string="生产工单"/>
                <field name="employee_id" string="自检人"/>
                <field name="check_date" string="自检时间"/>
                <field name="check_qty" string="自检数量"/>
                <field name="qualified_qty" string="合格数量"/>
                <field name="unqualified_qty" string="不合格数量"/>
                <field name="product_id" string="产品"/>
            </tree>
        </field>
    </record>

    <!-- Form视图 -->
    <record id="view_self_check_order_form" model="ir.ui.view">
        <field name="name">roke.self.check.order.form</field>
        <field name="model">roke.self.check.order</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <group>
                        <field name="task_id" string="生产任务"/>
                        <field name="work_order_id" string="生产工单"/>
                        <field name="employee_id" string="自检人"/>
                        <field name="check_date" string="自检时间"/>
                        <field name="product_id" string="产品"/>
                    </group>
                    <group>
                        <field name="check_qty" string="自检数量"/>
                        <field name="qualified_qty" string="合格数量"/>
                        <field name="unqualified_qty" string="不合格数量" readonly="1"/>
                        <field name="state" string="状态"/>
                    </group>
                </group>
            </form>
        </field>
    </record>
    <!-- Action -->
    <record id="action_self_check_order" model="ir.actions.act_window">
        <field name="name">自检单</field>
        <field name="res_model">roke.self.check.order</field>
        <field name="view_mode">tree,form</field>
    </record>
    <!-- 菜单项 -->
    <menuitem id="menu_self_check_order"
              name="自检单"
              parent="roke_mes_quality.roke_quality_business_menu"
              action="action_self_check_order"
              sequence="20"/>
</odoo>