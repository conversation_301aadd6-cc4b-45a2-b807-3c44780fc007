from odoo import api, fields, models


class InheritRokeProductionTask(models.Model):
    _inherit = 'roke.production.task'

    mold_id = fields.Many2one('roke.mold.ledger', string='模具')
    tool_id = fields.Many2one("roke.mes.tool", string='工装')
    equipment_id = fields.Many2one("roke.mes.equipment", string="设备")
    qualified_qty = fields.Float(string='合格数量', compute='_compute_qualified_qty', store=True)
    start_date = fields.Date(string='实际开始日期')
    finish_date = fields.Date(string='实际完成日期')
    equipment_wight = fields.Float(string='设备吨位')
    attachment_ids = fields.Many2many("ir.attachment", string='图号附件')
    container_record_ids = fields.One2many("roke.container.record", "task_id", string='更换容器记录')
    check_item_ids = fields.One2many("roke.task.eqpt.check.item", "task_id", string='任务设备点检记录')


    @api.depends('finish_qty', 'sum_unqualified_qty')
    def _compute_qualified_qty(self):
        for rec in self:
            rec.qualified_qty = rec.finish_qty - rec.sum_unqualified_qty
