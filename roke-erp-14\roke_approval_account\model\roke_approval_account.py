# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.inspur.com
"""
from odoo import models, fields, api, _
from odoo.addons.roke_all_approval.model.roke_all_approval import ALLOW_APPROVAL

# 付款单
class RokeMesPay(models.Model):
    _name = "roke.mes.pay"
    _inherit = ["approval.from", "mail.thread", "roke.mes.pay"]
    _rec_name = "code"

ALLOW_APPROVAL.update({"roke.mes.pay": "roke_mes_account"})

# 收款单
class RokeMesCollection(models.Model):
    _name = "roke.mes.collection"
    _inherit = ["approval.from", "mail.thread", "roke.mes.collection"]
    _rec_name = "code"

ALLOW_APPROVAL.update({"roke.mes.collection": "roke_mes_account"})

# 预收款单/预付款单
class RokeMesCollection(models.Model):
    _name = "roke.mes.payment"
    _inherit = ["approval.from", "mail.thread", "roke.mes.payment"]
    _rec_name = "code"

ALLOW_APPROVAL.update({"roke.mes.payment": "roke_mes_account"})

class InheritApprovalFlow(models.Model):
    _inherit = 'approval.flow'

    def _compute_domain(self):
        manual_models = [model.model for model in self.env['ir.model'].search([
            ('state', '=', 'manual'), ('transient', '!=', True)
        ])]
        # 处理卸载之后过滤已有数据
        ALLOW_MODEL = []
        model_obj = self.env['ir.model']
        for model in ALLOW_APPROVAL:
            model_id = model_obj.sudo().search([('model', '=', model)], limit=1)
            if model_id:
                if ALLOW_APPROVAL[model] in model_id.modules:
                    ALLOW_MODEL.append(model)
        return [('model', 'in', ALLOW_MODEL + manual_models)]

    model_id = fields.Many2one('ir.model', u'模型', domain=_compute_domain, index=1)