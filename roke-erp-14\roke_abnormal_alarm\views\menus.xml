<odoo>
    <!--安灯异常-->
    <menuitem id="roke_abnormal_safety_lamp_main_menu" name="安灯异常" sequence="360"
              web_icon="roke_abnormal_alarm,static/description/main_menu.png" groups="base.group_system"/>
    <menuitem id="roke_abnormal_safety_lamp_main_menu01" name="反馈" sequence="10"
              parent="roke_abnormal_safety_lamp_main_menu" groups="base.group_system"/>
    <menuitem id="roke_abnormal_safety_lamp_main_menu02" name="业务" sequence="20"
              parent="roke_abnormal_safety_lamp_main_menu" groups="base.group_system"/>
    <menuitem id="roke_abnormal_safety_lamp_main_menu03" name="基础数据" sequence="30"
              parent="roke_abnormal_safety_lamp_main_menu" groups="base.group_system"/>
    <menuitem id="roke_abnormal_safety_lamp_main_menu04" name="查询" sequence="40"
              parent="roke_abnormal_safety_lamp_main_menu" groups="base.group_system"/>
    <!--反馈-->
    <menuitem id="menu_roke_abnormal_alarm_kanban" name="异常看板" action="roke_abnormal_alarm_views_action"
              parent="roke_abnormal_safety_lamp_main_menu01" sequence="10"/>
    <!--业务-->
    <menuitem id="menu_roke_abnormal_alarm_response" name="异常响应" action="roke_abnormal_alarm_views_action"
              parent="roke_abnormal_safety_lamp_main_menu02" sequence="10"/>
    <menuitem id="menu_roke_abnormal_alarm_handle" name="异常处理" action="roke_abnormal_alarm_views_action"
              parent="roke_abnormal_safety_lamp_main_menu02" sequence="10"/>
    <!--基础数据-->
    <menuitem id="roke_abnormal_alarm_type_menu" name="异常类型" action="roke_abnormal_alarm_type_views_action"
              parent="roke_abnormal_safety_lamp_main_menu03" sequence="10" />
    <menuitem id="roke_abnormal_alarm_item_menu" name="异常项目" action="roke_abnormal_alarm_item_views_action"
              parent="roke_abnormal_safety_lamp_main_menu03" sequence="20"/>
    <menuitem id="roke_abnormal_alarm_state_menu" name="处理状态" action="roke_abnormal_alarm_state_views_action"
              parent="roke_abnormal_safety_lamp_main_menu03" sequence="30"/>
    <!--查询-->
    <!--TODO 异常响应查询/异常处理统计/异常知识库-->
    <!--报工异常查询-->
    <menuitem id="roke_work_abnormal_record_wizard_menu" name="报工异常查询" parent="roke_mes_production.roke_production_report_menu"
              action="view_roke_work_abnormal_record_wizard_action" sequence="120" report="1"
              groups="base.group_system"/>
</odoo>
