#!/usr/bin/env python
# -*- coding:utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
from odoo.addons.roke_pub_access.models.inherit_res_groups import DEFAULT_X_GROUPS
from lxml import etree

# 将创建用户默认为管理员去除
DEFAULT_X_GROUPS.update({
    "base.group_system": 0
})

# MES相关顶级菜单
TOP_MENUS_LIST = [

]

class RokeMesInheritGroup(models.Model):
    _inherit = 'res.groups'

    def get_submenu_list(self, parent_menu_id, result):
        """
        获取下级菜单
        """
        menu_obj = self.sudo().env['ir.ui.menu']
        menu_ids = menu_obj.search([('active', '=', True), ('parent_id', '=', parent_menu_id.id)])
        if menu_ids:
            for menu in menu_ids:
                result.append(menu.id)
                self.get_submenu_list(menu, result)
        else:
            result.append(parent_menu_id.id)
        return result

    @api.model
    def create(self, vals):
        res = super(RokeMesInheritGroup, self).create(vals)
        # 新建角色，赋予指定的菜单
        top_menu = []
        res.menu_access = top_menu
        # 新建角色，赋予所有按钮权限
        res.ir_button_ids = self.sudo().env['roke.ir.ui.button'].search([]).ids
        # 新建角色，赋予所有Mes功能
        domain = [('active', '=', True)]
        res.app_function_ids = self.sudo().env['roke.app.function'].search(domain).ids
        return res

    # 过滤隐藏，去除为空报错
    def get_show_roles(self):
        group_data_ids = self.env["ir.model.data"].sudo().search([("model", "=", "res.groups")])
        system_group_ids = self.sudo().search([("id", "in", group_data_ids.mapped("res_id"))])
        special_groups = [

        ]
        special_group_ids = [
            self.env.ref(special_group).id
            for special_group in special_groups if self.env.ref(special_group, raise_if_not_found=False)
        ]
        system_group_id_list = system_group_ids.ids
        filter_ids = [group_id for group_id in system_group_id_list if group_id not in special_group_ids]
        group_ids = self.sudo().search([("id", "not in", filter_ids)]).filtered(lambda g: not g.is_hide)
        return group_ids

class InheritMesRokeIrUiFields(models.Model):
    _inherit = "roke.pub.ir.fields"

    @api.onchange("model_id")
    def _onchange_model_id(self):
        if self.model_id:
            return {
                'domain': {
                    'fields_id': [('model_id', '=', self.model_id.id)],
                },
            }
