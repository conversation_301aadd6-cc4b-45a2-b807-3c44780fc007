<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--质检单-->
    <!--search-->
    <record id="view_roke_quality_order_search" model="ir.ui.view">
        <field name="name">roke.quality.order.search</field>
        <field name="model">roke.quality.order</field>
        <field name="arch" type="xml">
            <search string="质检单">
                <field name="code"/>
                <field name="quality_request_id"/>
                <field name="department_id"/>
                <field name="employee_ids"/>
                <field name="product_id"/>
                <separator/>
                <filter string="全检" name="全检" domain="[('check_scope', '=', '全检')]"/>
                <filter string="抽检" name="抽检" domain="[('check_scope', '=', '抽检')]"/>
                <filter string="首件检" name="首件检" domain="[('check_scope', '=', '首件检')]"/>
                <separator/>
                <filter string="草稿" name="草稿" domain="[('state', '=', '草稿')]"/>
                <filter string="完成" name="完成" domain="[('state', '=', '完成')]"/>
                <filter string="取消" name="取消" domain="[('state', '=', '取消')]"/>
                <group expand="0" string="Group By">
                    <filter string="产品" name="groupby_product" domain="[]" context="{'group_by':'product_id'}"/>
                    <filter string="质检部门" name="groupby_department" domain="[]"
                            context="{'group_by':'department_id'}"/>
                    <filter string="业务日期" name="groupby_order_date" domain="[]"
                            context="{'group_by':'order_date'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="views_inherit_roke_quality_order_tree" model="ir.ui.view">
        <field name="name">inherit.roke.quality.order.tree</field>
        <field name="model">roke.quality.order</field>
        <field name="inherit_id" ref="roke_mes_quality_enterprise.view_roke_quality_order_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//tree" position="replace">
                <tree string="质检单" decoration-info="state == '草稿'" decoration-muted="state == '取消'">
                    <field name="code"/>
                    <field name="department_id"/>
                    <field name="employee_ids" widget="many2many_tags"/>
                    <field name="order_date"/>
                    <field name="scheme_id"/>
                    <field name="check_scope"/>
                    <field name="plan_qty" sum="合计"/>
                    <field name="pass_qty" sum="合计"/>
                    <field name="fail_qty" sum="合计"/>
                    <field name="state" invisible="1"/>
                </tree>
            </xpath>
        </field>
    </record>

    <record id="views_inherit_roke_quality_order_xj_tree" model="ir.ui.view">
        <field name="name">inherit.roke.quality.order.tree</field>
        <field name="model">roke.quality.order</field>
        <field name="arch" type="xml">
            <tree string="质检单" decoration-info="state == '草稿'" decoration-muted="state == '取消'">
                <field name="code"/>
                <field name="department_id"/>
                <field name="employee_ids" widget="many2many_tags"/>
                <field name="order_date"/>
                <field name="scheme_id"/>
                <field name="check_scope"/>
                <field name="plan_qty" sum="合计"/>
                <field name="pass_qty" sum="合计"/>
                <field name="fail_qty" sum="合计"/>
                <field name="state" invisible="1"/>
            </tree>
        </field>
    </record>

    <record id="views_inherit_roke_quality_order_dh_tree" model="ir.ui.view">
        <field name="name">inherit.roke.quality.order.tree</field>
        <field name="model">roke.quality.order</field>
        <field name="arch" type="xml">
            <tree string="质检单" decoration-info="state == '草稿'" decoration-muted="state == '取消'">
                <field name="code"/>
                <field name="department_id"/>
                <field name="employee_ids" widget="many2many_tags"/>
                <field name="order_date"/>
                <field name="scheme_id"/>
                <field name="check_scope"/>
                <field name="plan_qty" sum="合计"/>
                <field name="pass_qty" sum="合计"/>
                <field name="fail_qty" sum="合计"/>
                <field name="state" invisible="1"/>
            </tree>
        </field>
    </record>
    <record id="views_inherit_roke_quality_order_fh_tree" model="ir.ui.view">
        <field name="name">inherit.roke.quality.order.tree</field>
        <field name="model">roke.quality.order</field>
        <field name="arch" type="xml">
            <tree string="质检单" decoration-info="state == '草稿'" decoration-muted="state == '取消'">
                <field name="code"/>
                <field name="department_id"/>
                <field name="employee_ids" widget="many2many_tags"/>
                <field name="order_date"/>
                <field name="scheme_id"/>
                <field name="check_scope"/>
                <field name="plan_qty" sum="合计"/>
                <field name="pass_qty" sum="合计"/>
                <field name="fail_qty" sum="合计"/>
                <field name="state" invisible="1"/>
            </tree>
        </field>
    </record>

    <!--form-->

    <record id="views_inherit_roke_quality_order_form" model="ir.ui.view">
        <field name="name">inherit.roke.quality.order.form</field>
        <field name="model">roke.quality.order</field>
        <field name="inherit_id" ref="roke_mes_quality_enterprise.view_roke_quality_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//form" position="replace">
                <form string="质检单">
                    <header>
                        <button name="make_finish" type="object" string="完成" class="oe_highlight"
                                attrs="{'invisible': [('state', '!=', '草稿')]}"/>
                        <button name="make_draft" type="object" string="置为草稿"
                                attrs="{'invisible': [('state', '=', '草稿')]}"/>
                        <button name="action_create_sample" type="object" string="取样" class="oe_highlight"
                                attrs="{'invisible': [('state', '!=', '草稿')]}"/>
                        <field name="state" widget="statusbar"/>
                    </header>
                    <div name="button_box" class="oe_button_box"/>
                    <group id="g1" col="4">
                        <group>
                            <field name="code" readonly="1" force_save="1"/>
                            <field name="order_date" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                            <field name="pass_qty" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                            <field name="result" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                            <field name="container_id" readonly="1"/>
                        </group>
                        <group>
                            <field name="quality_type" attrs="{'readonly': [('state', '!=', '草稿')]}" required="1"/>
                            <field name="scheme_id" options="{'no_create': True}" domain="[('state', '=', '生效中')]"
                                   attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                            <field name="fail_qty" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                            <field name="stock_picking_id"/>
                        </group>
                        <group>
                            <field name="department_id" options="{'no_create': True}"
                                   attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                            <field name="product_id" options="{'no_create': True}"
                                   attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                            <field name="task_id"/>
                            <field name="workshop_id"/>
                        </group>
                        <group>
                            <field name="employee_ids" options="{'no_create': True}" widget="many2many_tags"
                                   attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                            <field name="plan_qty" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                            <field name="pass_rate" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                            <field name="purchase_order_id"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="质检明细">
                            <field name="line_ids" attrs="{'readonly': [('state', '!=', '草稿')]}">
                                <tree editable="bottom">
                                    <field name="id" invisible="1"/>
                                    <field name="order_id" invisible="1"/>
                                    <field name="scheme_line_id" invisible="1"/>
                                    <field name="sequence" widget="handle"/>
                                    <field name="item_id" options="{'no_create': True}"
                                           attrs="{'readonly': [('scheme_line_id', '!=', False)]}"/>
                                    <field name="item_id" invisible="1"/>
                                    <field name="standard_value" readonly="1" force_save="1"/>
                                    <field name="result"/>
                                    <field name="auto_judge"/>
                                    <field name="analysis_method"/>
                                    <field name="quality_method_id"/>
                                    <field name="destructive"/>
                                    <field name="key"/>
                                    <field name="finish_time"/>
                                    <field name="note"/>
                                    <field name="qty"/>
                                    <field name="judge_target" invisible="1"/>
                                    <field name="operator" invisible="1"/>
                                    <field name="upper_limit" invisible="1"/>
                                    <field name="upper_limit_tolerance" invisible="1"/>
                                    <field name="lower_limit" invisible="1"/>
                                    <field name="lower_limit_tolerance" invisible="1"/>
                                    <button name='action_set_indicator' string='检验指标' type='object'
                                            class='oe_highlight'
                                            attrs="{'invisible': [('scheme_line_id', '!=', False)]}"/>
                                </tree>
                                <form>
                                    <group col="3">
                                        <group>
                                            <field name="order_id"/>
                                            <field name="item_id" options="{'no_create': True}"/>
                                            <field name="standard_value"/>
                                        </group>
                                        <group>
                                            <field name="result"/>
                                            <field name="auto_judge"/>
                                            <field name="analysis_method"/>
                                        </group>
                                        <group>
                                            <field name="destructive"/>
                                            <field name="key"/>
                                            <field name="finish_time"/>
                                            <field name="note"/>
                                        </group>
                                    </group>
                                </form>
                            </field>
                        </page>
                    </notebook>
                    <group id="g2">
                        <field name="note" placeholder="此处可以填写备注或描述"/>
                    </group>
                    <div class="oe_chatter">
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </xpath>
        </field>
    </record>

    <record id="views_inherit_roke_quality_order_xj_form" model="ir.ui.view">
        <field name="name">inherit.roke.quality.order.form</field>
        <field name="model">roke.quality.order</field>
        <field name="arch" type="xml">
            <form string="巡检单">
                <header>
                    <button name="make_finish" type="object" string="完成" class="oe_highlight"
                            attrs="{'invisible': [('state', '!=', '草稿')]}"/>
                    <button name="make_draft" type="object" string="置为草稿"
                            attrs="{'invisible': [('state', '=', '草稿')]}"/>
                    <button name="action_create_sample" type="object" string="取样" class="oe_highlight"
                            attrs="{'invisible': [('state', '!=', '草稿')]}"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <div name="button_box" class="oe_button_box"/>
                <group id="g1" col="4">
                    <group>
                        <field name="code" readonly="1" force_save="1"/>
                        <field name="order_date" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="pass_qty" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="result" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="container_id" readonly="1"/>
                    </group>
                    <group>
                        <field name="quality_type" readonly="1" force_save="1"/>
                        <field name="scheme_id" options="{'no_create': True}" domain="[('state', '=', '生效中'), ('type', '=', '巡检')]"
                               attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="fail_qty" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="stock_picking_id"/>
                    </group>
                    <group>
                        <field name="department_id" options="{'no_create': True}"
                               attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="product_id" options="{'no_create': True}"
                               attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="task_id"/>
                        <field name="workshop_id"/>
                    </group>
                    <group>
                        <field name="employee_ids" options="{'no_create': True}" widget="many2many_tags"
                               attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="plan_qty" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="pass_rate" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="purchase_order_id"/>
                    </group>
                </group>
                <notebook>
                    <page string="质检明细">
                        <field name="line_ids" attrs="{'readonly': [('state', '!=', '草稿')]}">
                            <tree editable="bottom">
                                <field name="id" invisible="1"/>
                                <field name="order_id" invisible="1"/>
                                <field name="scheme_line_id" invisible="1"/>
                                <field name="sequence" widget="handle"/>
                                <field name="item_id" options="{'no_create': True}"
                                       attrs="{'readonly': [('scheme_line_id', '!=', False)]}"/>
                                <field name="item_id" invisible="1"/>
                                <field name="standard_value" readonly="1" force_save="1"/>
                                <field name="result"/>
                                <field name="auto_judge"/>
                                <field name="analysis_method"/>
                                <field name="quality_method_id"/>
                                <field name="destructive"/>
                                <field name="key"/>
                                <field name="finish_time"/>
                                <field name="note"/>
                                <field name="qty"/>
                                <field name="judge_target" invisible="1"/>
                                <field name="operator" invisible="1"/>
                                <field name="upper_limit" invisible="1"/>
                                <field name="upper_limit_tolerance" invisible="1"/>
                                <field name="lower_limit" invisible="1"/>
                                <field name="lower_limit_tolerance" invisible="1"/>
                                <button name='action_set_indicator' string='检验指标' type='object'
                                        class='oe_highlight'
                                        attrs="{'invisible': [('scheme_line_id', '!=', False)]}"/>
                            </tree>
                            <form>
                                <group col="3">
                                    <group>
                                        <field name="order_id"/>
                                        <field name="item_id" options="{'no_create': True}"/>
                                        <field name="standard_value"/>
                                    </group>
                                    <group>
                                        <field name="result"/>
                                        <field name="auto_judge"/>
                                        <field name="analysis_method"/>
                                    </group>
                                    <group>
                                        <field name="destructive"/>
                                        <field name="key"/>
                                        <field name="finish_time"/>
                                        <field name="note"/>
                                    </group>
                                </group>
                            </form>
                        </field>
                    </page>
                </notebook>
                <group id="g2">
                    <field name="note" placeholder="此处可以填写备注或描述"/>
                </group>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>

    <record id="views_inherit_roke_quality_order_dh_form" model="ir.ui.view">
        <field name="name">inherit.roke.quality.order.form</field>
        <field name="model">roke.quality.order</field>
        <field name="arch" type="xml">
            <form string="到货检">
                <header>
                    <button name="make_finish" type="object" string="完成" class="oe_highlight"
                            attrs="{'invisible': [('state', '!=', '草稿')]}"/>
                    <button name="make_draft" type="object" string="置为草稿"
                            attrs="{'invisible': [('state', '=', '草稿')]}"/>
                    <button name="action_create_sample" type="object" string="取样" class="oe_highlight"
                            attrs="{'invisible': [('state', '!=', '草稿')]}"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <div name="button_box" class="oe_button_box"/>
                <group id="g1" col="4">
                    <group>
                        <field name="code" readonly="1" force_save="1"/>
                        <field name="order_date" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="pass_qty" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="result" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="container_id" readonly="1"/>
                    </group>
                    <group>
                        <field name="quality_type" readonly="1" force_save="1"/>
                        <field name="scheme_id" options="{'no_create': True}" domain="[('state', '=', '生效中'), ('type', '=', '到货检')]"
                               attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="fail_qty" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="stock_picking_id"/>
                    </group>
                    <group>
                        <field name="department_id" options="{'no_create': True}"
                               attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="product_id" options="{'no_create': True}"
                               attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="task_id"/>
                        <field name="workshop_id"/>
                    </group>
                    <group>
                        <field name="employee_ids" options="{'no_create': True}" widget="many2many_tags"
                               attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="plan_qty" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="pass_rate" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="purchase_order_id"/>
                    </group>
                </group>
                <notebook>
                    <page string="质检明细">
                        <field name="line_ids" attrs="{'readonly': [('state', '!=', '草稿')]}">
                            <tree editable="bottom">
                                <field name="id" invisible="1"/>
                                <field name="order_id" invisible="1"/>
                                <field name="scheme_line_id" invisible="1"/>
                                <field name="sequence" widget="handle"/>
                                <field name="item_id" options="{'no_create': True}"
                                       attrs="{'readonly': [('scheme_line_id', '!=', False)]}"/>
                                <field name="item_id" invisible="1"/>
                                <field name="standard_value" readonly="1" force_save="1"/>
                                <field name="result"/>
                                <field name="auto_judge"/>
                                <field name="analysis_method"/>
                                <field name="quality_method_id"/>
                                <field name="destructive"/>
                                <field name="key"/>
                                <field name="finish_time"/>
                                <field name="note"/>
                                <field name="qty"/>
                                <field name="judge_target" invisible="1"/>
                                <field name="operator" invisible="1"/>
                                <field name="upper_limit" invisible="1"/>
                                <field name="upper_limit_tolerance" invisible="1"/>
                                <field name="lower_limit" invisible="1"/>
                                <field name="lower_limit_tolerance" invisible="1"/>
                                <button name='action_set_indicator' string='检验指标' type='object'
                                        class='oe_highlight'
                                        attrs="{'invisible': [('scheme_line_id', '!=', False)]}"/>
                            </tree>
                            <form>
                                <group col="3">
                                    <group>
                                        <field name="order_id"/>
                                        <field name="item_id" options="{'no_create': True}"/>
                                        <field name="standard_value"/>
                                    </group>
                                    <group>
                                        <field name="result"/>
                                        <field name="auto_judge"/>
                                        <field name="analysis_method"/>
                                    </group>
                                    <group>
                                        <field name="destructive"/>
                                        <field name="key"/>
                                        <field name="finish_time"/>
                                        <field name="note"/>
                                    </group>
                                </group>
                            </form>
                        </field>
                    </page>
                </notebook>
                <group id="g2">
                    <field name="note" placeholder="此处可以填写备注或描述"/>
                </group>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>

    <record id="views_inherit_roke_quality_order_fh_form" model="ir.ui.view">
        <field name="name">inherit.roke.quality.order.form</field>
        <field name="model">roke.quality.order</field>
        <field name="arch" type="xml">
            <form string="发货检">
                <header>
                    <button name="make_finish" type="object" string="完成" class="oe_highlight"
                            attrs="{'invisible': [('state', '!=', '草稿')]}"/>
                    <button name="make_draft" type="object" string="置为草稿"
                            attrs="{'invisible': [('state', '=', '草稿')]}"/>
                    <button name="action_create_sample" type="object" string="取样" class="oe_highlight"
                            attrs="{'invisible': [('state', '!=', '草稿')]}"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <div name="button_box" class="oe_button_box"/>
                <group id="g1" col="4">
                    <group>
                        <field name="code" readonly="1" force_save="1"/>
                        <field name="order_date" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="pass_qty" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="result" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="container_id" readonly="1"/>
                    </group>
                    <group>
                        <field name="quality_type" readonly="1" force_save="1"/>
                        <field name="scheme_id" options="{'no_create': True}" domain="[('state', '=', '生效中'), ('type', '=', '发货检')]"
                               attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="fail_qty" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="stock_picking_id"/>
                    </group>
                    <group>
                        <field name="department_id" options="{'no_create': True}"
                               attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="product_id" options="{'no_create': True}"
                               attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="task_id"/>
                        <field name="workshop_id"/>
                    </group>
                    <group>
                        <field name="employee_ids" options="{'no_create': True}" widget="many2many_tags"
                               attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="plan_qty" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="pass_rate" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        <field name="purchase_order_id"/>
                    </group>
                </group>
                <notebook>
                    <page string="质检明细">
                        <field name="line_ids" attrs="{'readonly': [('state', '!=', '草稿')]}">
                            <tree editable="bottom">
                                <field name="id" invisible="1"/>
                                <field name="order_id" invisible="1"/>
                                <field name="scheme_line_id" invisible="1"/>
                                <field name="sequence" widget="handle"/>
                                <field name="item_id" options="{'no_create': True}"
                                       attrs="{'readonly': [('scheme_line_id', '!=', False)]}"/>
                                <field name="item_id" invisible="1"/>
                                <field name="standard_value" readonly="1" force_save="1"/>
                                <field name="result"/>
                                <field name="auto_judge"/>
                                <field name="analysis_method"/>
                                <field name="quality_method_id"/>
                                <field name="destructive"/>
                                <field name="key"/>
                                <field name="finish_time"/>
                                <field name="note"/>
                                <field name="qty"/>
                                <field name="judge_target" invisible="1"/>
                                <field name="operator" invisible="1"/>
                                <field name="upper_limit" invisible="1"/>
                                <field name="upper_limit_tolerance" invisible="1"/>
                                <field name="lower_limit" invisible="1"/>
                                <field name="lower_limit_tolerance" invisible="1"/>
                                <button name='action_set_indicator' string='检验指标' type='object'
                                        class='oe_highlight'
                                        attrs="{'invisible': [('scheme_line_id', '!=', False)]}"/>
                            </tree>
                            <form>
                                <group col="3">
                                    <group>
                                        <field name="order_id"/>
                                        <field name="item_id" options="{'no_create': True}"/>
                                        <field name="standard_value"/>
                                    </group>
                                    <group>
                                        <field name="result"/>
                                        <field name="auto_judge"/>
                                        <field name="analysis_method"/>
                                    </group>
                                    <group>
                                        <field name="destructive"/>
                                        <field name="key"/>
                                        <field name="finish_time"/>
                                        <field name="note"/>
                                    </group>
                                </group>
                            </form>
                        </field>
                    </page>
                </notebook>
                <group id="g2">
                    <field name="note" placeholder="此处可以填写备注或描述"/>
                </group>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>

    <record id="view_roke_quality_order_action" model="ir.actions.act_window">
        <field name="name">质检单</field>
        <field name="res_model">roke.quality.order</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                点击左上角“创建”按钮新增一个质检单。
            </p>
        </field>
    </record>

    <!-- 巡检 -->
    <record id="view_roke_quality_order_xj_action" model="ir.actions.act_window">
        <field name="name">巡检</field>
        <field name="res_model">roke.quality.order</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[('quality_type', '=', '巡检')]</field>
        <field name="search_view_id" ref="view_roke_quality_order_search"/>
        <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('roke_mes_wylj.views_inherit_roke_quality_order_xj_tree')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('roke_mes_wylj.views_inherit_roke_quality_order_xj_form')})]"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                点击左上角“创建”按钮新增一个巡检。
            </p>
        </field>
        <field name="context">{'default_quality_type': '巡检'}</field>
    </record>

    <record id="view_roke_quality_order_fh_action" model="ir.actions.act_window">
        <field name="name">发货检</field>
        <field name="res_model">roke.quality.order</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[('quality_type', '=', '发货检')]</field>
        <field name="search_view_id" ref="view_roke_quality_order_search"/>
        <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('roke_mes_wylj.views_inherit_roke_quality_order_fh_tree')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('roke_mes_wylj.views_inherit_roke_quality_order_fh_form')})]"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                点击左上角“创建”按钮新增一个发货检单。
            </p>
        </field>
        <field name="context">{'default_quality_type': '发货检'}</field>
    </record>

    <record id="view_roke_quality_order_dh_action" model="ir.actions.act_window">
        <field name="name">到货检</field>
        <field name="res_model">roke.quality.order</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[('quality_type', '=', '到货检')]</field>
        <field name="search_view_id" ref="view_roke_quality_order_search"/>
        <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('roke_mes_wylj.views_inherit_roke_quality_order_dh_tree')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('roke_mes_wylj.views_inherit_roke_quality_order_dh_form')})]"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                点击左上角“创建”按钮新增一个到货检单。
            </p>
        </field>
        <field name="context">{'default_quality_type': '到货检'}</field>
    </record>


    <record id="inherit_roke_quality_scheme_form" model="ir.ui.view">
        <field name="name">inherit.roke.quality.scheme.form</field>
        <field name="model">roke.quality.scheme</field>
        <field name="inherit_id" ref="roke_mes_quality_enterprise.view_roke_quality_scheme_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='employee_id']" position="after">
                <field name="type"/>
            </xpath>
        </field>
    </record>

</odoo>
