# -*- coding: utf-8 -*-
"""
Description:
    模具管理
Versions:
    Created by www.inspur.com
"""
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import datetime, re, math
from datetime import timedelta
import threading, logging, requests, json, base64, qrcode, io

_logger = logging.getLogger(__name__)

def make_qr(string):
    qr = qrcode.QRCode(
        version=4,  # 生成二维码尺寸的大小 1-40 1:21*21（21+(n-1)*4）
        error_correction=qrcode.constants.ERROR_CORRECT_M,  # L:7% M:15% Q:25% H:30%
        box_size=8,  # 每个格子的像素大小
        border=2,  # 边框的格子宽度大小
    )
    qr.add_data(string)
    qr.make(fit=True)
    img = qr.make_image()
    buf = io.BytesIO()
    img.save(buf)
    img_stream = buf.getvalue()
    return base64.b64encode(img_stream)


class RokeMoldLedger(models.Model):
    _name = "roke.mold.ledger"
    _description = "模具台账"
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _rec_name = "name"

    name = fields.Char(string="模具名称", tracking=True, required=True)
    code = fields.Char(string="厂内模具编号", tracking=True, required=True, copy=False,
                       default=lambda self: self.env['ir.sequence'].next_by_code('roke.mold.code'))
    arrival_time = fields.Date(string="到厂时间", default=fields.Date.context_today, tracking=True)
    specification = fields.Char(string="规格", tracking=True)
    use_customer = fields.Char(string="使用客户", tracking=True)
    supplier_factory = fields.Char(string="模具供应厂家", tracking=True)
    mold_out_code = fields.Char(string="模具出厂编号", tracking=True)
    state = fields.Selection([
        ('闲置', '闲置'), ('在产', '在产'), ('保养', '保养'), ('维修', '维修'), ('报废', '报废'), ('封存', '封存')
    ], string="模具状态", default='闲置', readonly=True, tracking=True)
    manufacturer = fields.Char(string="生产厂商", tracking=True)
    cavities_number = fields.Integer(string="腔数", tracking=True)
    note = fields.Text(string="说明", tracking=True)
    spare_part = fields.Char(string="备件", tracking=True)
    use_mold_frequency = fields.Char(string="使用模具次数", tracking=True)
    mold_current_state = fields.Char(string="模具当前状态", tracking=True)
    material = fields.Char(string="材质", tracking=True)
    molding_time = fields.Char(string="成型时间", tracking=True)
    service_time = fields.Char(string='保养频次')
    service_intervals = fields.Char(string='保养周期')
    responsible_person = fields.Char(string='责任人', tracking=True)
    argument_sheet = fields.Binary(string='论证单')
    purchase_contracts = fields.Binary(string='购置合同')
    instructions = fields.Binary(string='设备使用说明书')
    acceptance = fields.Binary(string='验收报告')
    recipient = fields.Char(string='接收人', tracking=True)
    mold_size = fields.Char(string='模具尺寸', tracking=True)
    spare_circumstance = fields.Char(string='备件情况', tracking=True)
    temperature_box_location = fields.Char(string='温控箱位置', tracking=True)
    mandrels_number = fields.Float(string='可用芯棒数量', tracking=True)
    collars_number = fields.Float(string='可用颈环数量', tracking=True)
    cavity_number = fields.Char(string='腔号', tracking=True)

    workshop_id = fields.Many2one("roke.workshop", string="车间", tracking=True)
    work_center_id = fields.Many2one("roke.work.center", string="工作中心", tracking=True)
    equipment_id = fields.Many2one("roke.mes.equipment", string="当前设备", tracking=True)
    start_use_life = fields.Integer(string="初始使用寿命", tracking=True)
    standard_use_life = fields.Integer(string="标准使用寿命", tracking=True)
    current_use_life = fields.Integer(string="当前使用寿命", compute="_compute_current_use_life")
    mold_location_id = fields.Many2one("roke.mes.stock.location", string="模具位置", domain="[('is_hardware', '=', True)]", tracking=True)
    location_id = fields.Many2one("roke.mold.location", string="模具位置", tracking=True)
    location = fields.Char(string="模具位置", related="mold_location_id.name")
    maintain_records = fields.One2many("roke.mold.maintain.records", "mold_id", string="保养记录")
    repair_records = fields.One2many("roke.mold.repair.records", "mold_id", string="维修记录")
    pick_records = fields.One2many("roke.mold.pick.records", "mold_id", string="数采记录")
    use_records = fields.One2many("roke.mold.use.records", "line_id", string="使用记录")
    day_production = fields.Float(string="日产量")
    department_id = fields.Many2one('roke.department', string="部门", tracking=True)
    inspection_plan_id = fields.Many2one(string="点检方案", comodel_name="mold.inspection.plan", tracking=True)
    mold_maintain_id = fields.Many2one(string="保养方案", comodel_name="roke.mold.maintain", tracking=True)
    inspection_record_ids = fields.One2many(string="点检记录", comodel_name="mold.inspection.record", inverse_name="mold_id")
    asset_number = fields.Char(string="资产编号", tracking=True)
    category_id = fields.Many2one(string="模具类别", comodel_name="mold.category")
    qr_code = fields.Char(string="二维码内容", tracking=True)
    part_number = fields.Char(string="部品号", tracking=True)
    work_team_id = fields.Many2one("roke.work.team", string="所属班组", tracking=True)

    roke_company_id = fields.Many2one('roke.company', string='当前公司')
    current_location_id = fields.Many2one("roke.mes.stock.location", string="当前仓库",
                                          domain="[('is_hardware', '=', True)]", tracking=True)
    ck_location_id = fields.Many2one("roke.mold.location", string="仓库", domain="[('type', '=', '仓库')]", tracking=True)
    kq_location_id = fields.Many2one("roke.mold.location", string="库区", domain="[('type', '=', '库区')]", tracking=True)
    kw_location_id = fields.Many2one("roke.mold.location", string="库位", domain="[('type', '=', '库位')]", tracking=True)
    maintain_user_ids = fields.Many2many("res.users", string="保养人")
    is_common_use = fields.Boolean(string="是否常用", tracking=True)

    _sql_constraints = [
        ('code_unique', 'UNIQUE(code)', '编号已存在，不可重复。如使用系统默认编号请刷新页面；如使用自定义编号请先删除重复的编号')
    ]

    @api.model
    def create(self, vals):
        if not vals.get("qr_code", ''):
            vals.update({'qr_code': vals["code"]})
        return super(RokeMoldLedger, self).create(vals)

    @api.model
    def create_maintain_order(self):
        for item in self:
            if item.service_intervals and len(item.maintain_records) % int(item.service_intervals) == 0:
                # 生成保养单
                self.env['roke.mold.maintain.records'].create({
                    'mold_id': item.id,
                    'maintain_id': item.mold_maintain_id.id,
                    'maintain_user': [(6, 0, item.maintain_user_ids.ids)]
                })

    def btn_cr_qr_image(self):
        for record in self:
            # 生成
            record.qr_image = make_qr(record.qr_code)

    def name_get(self):
        res = []
        for record in self:
            name = "%s(%s)" % (record.name, record.code)
            res.append((record.id, name))
        return res

    # 计算当前使用寿命，初始加采集明细之和
    def _compute_current_use_life(self):
        for record in self:
            current_use_life = record.start_use_life
            for rec in record.pick_records:
                current_use_life = current_use_life + rec.pick_data
            record.current_use_life = current_use_life

    # 模具报修
    def mold_warranty(self):
        if self.state in ('报废', '封存'):
            raise ValidationError('模具【%s】已报废或封存，禁止维修！' % self.name)
        return {
            'name': '模具报修',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'target': 'new',
            'res_model': 'roke.warranty.wizard',
            'context': {'default_mold_id': self.id,
                        'default_type': '报修'}
        }

    def action_scrap_mold(self):
        """
        模具报废功能
        将模具状态设置为报废
        """
        for record in self:
            # 检查模具当前状态
            if record.state == '报废':
                raise ValidationError(f"模具【{record.name}】已经是报废状态，无需重复操作！")

            # 检查模具是否在使用中
            if record.state == '在产':
                raise ValidationError(f"模具【{record.name}】正在生产中，无法报废！请先停止使用。")

            # 检查模具是否在维修中
            if record.state == '维修':
                raise ValidationError(f"模具【{record.name}】正在维修中，无法报废！请先完成维修。")

            # 检查是否有未完成的维修记录
            unfinished_repairs = self.env['roke.mold.maintain.records'].search([
                ('mold_id', '=', record.id),
                ('state', 'not in', ['完成', '取消'])
            ])
            if unfinished_repairs:
                raise ValidationError(f"模具【{record.name}】存在未完成的维修/保养记录，无法报废！请先处理完相关记录。")

            # 更新模具状态为报废
            record.write({'state': '报废'})

            # 记录操作日志
            record.message_post(
                body=f"模具已报废，操作人：{self.env.user.name}，操作时间：{fields.Datetime.now()}",
                message_type='notification'
            )

    def action_cancel_scrap_mold(self):
        """
        取消模具报废
        将模具状态从报废恢复为闲置
        """
        for record in self:
            # 检查模具当前状态
            if record.state != '报废':
                raise ValidationError(f"模具【{record.name}】不是报废状态，无法取消报废！")

            # 恢复模具状态
            record.write({'state': '闲置'})

            # 记录操作日志
            record.message_post(
                body=f"模具报废已取消，恢复为闲置状态，操作人：{self.env.user.name}，操作时间：{fields.Datetime.now()}",
                message_type='notification'
            )


class RokeMoldMaintainItem(models.Model):
    _name = "roke.mold.maintain.item"
    _description = "保养项目"

    code = fields.Char(string="编号", default=lambda self: self.env['ir.sequence'].next_by_code('roke.mold.maintain.item.code'))
    name = fields.Char(string="名称")
    note = fields.Text(string="保养内容")
    secure_text = fields.Char(string="安全措施")
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)


class RokeMoldMaintain(models.Model):
    _name = "roke.mold.maintain"
    _description = "模具保养方案"
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _rec_name = "name"

    name = fields.Char(string="保养方案", tracking=True, required=True)
    code = fields.Char(string="保养方案编号", tracking=True, required=True, copy=False,
                       default=lambda self: self.env['ir.sequence'].next_by_code('roke.mold.maintain.code'))
    mold_ids = fields.Many2many("roke.mold.ledger", string="保养模具")
    frequency = fields.Integer(string="保养频率", default=1)
    frequency_unit = fields.Selection([("年", "年"), ("月", "月"), ("周", "周"), ("日", "日")], string="保养频率单位", default="年")
    show_frequency = fields.Char(string="保养频率", compute="_compute_show_frequency")
    frequency_days = fields.Integer(string="保养频率（天）", compute="_compute_frequency_days", store=True)
    next_maintenance_date = fields.Date(string="下次保养日期", compute="_compute_next_maintenance_date", store=True)
    last_maintenance_date = fields.Date(string="上次保养日期")
    ir_cron = fields.Many2one("ir.cron", string="自动动作")
    cron_count = fields.Integer(string="定时任务数量", compute="_compute_cron_count")
    note = fields.Text(string="备注", tracking=True)
    maintain_records = fields.One2many("roke.mold.maintain.records", "maintain_id", string="保养记录")
    item_ids = fields.Many2many("roke.mold.maintain.item", string="保养项目")
    security_measures = fields.Char(string="安全措施", tracking=True)

    # 定时任务联查按钮
    def action_view_cron(self):
        cron_records = self.env["ir.cron"].browse(self.ir_cron.id)
        if len(cron_records) == 1:
            result = {
                'type': 'ir.actions.act_window',
                'name': "定时任务",
                'res_model': 'ir.cron',
                'view_mode': 'form',
                'context': {
                    'create': False
                },
                'res_id': cron_records.id,
                'views': [
                    (self.env.ref('base.ir_cron_view_form').id, 'form')
                ]
            }
        else:
            result = {
                'type': 'ir.actions.act_window',
                'name': "定时任务",
                'res_model': 'ir.cron',
                'view_mode': 'tree,form',
                'domain': "[('id','in',%s)]" % (cron_records.ids),
                'context': {
                    'create': False
                },
                'views': [
                    (self.env.ref('base.ir_cron_view_tree').id, 'tree'),
                    (self.env.ref('base.ir_cron_view_form').id, 'form')
                ]
            }
        return result

    # 定时任务数量
    def _compute_cron_count(self):
        for record in self:
            cron_records = record.env["ir.cron"].browse(self.ir_cron.id)
            record.cron_count = len(cron_records)

    # 创建保养任务
    def create_maintenance_order(self, res):
        maintain_id = self.sudo().env['roke.mold.maintain'].browse(int(res))
        for mold_id in maintain_id.mold_ids:
            self.sudo().env['roke.mold.maintain.records'].create({
                'mold_id': mold_id.id,
                'state': '草稿',
                'maintain_id': maintain_id.id,
                'note': maintain_id.note,
                'workshop_id': mold_id.workshop_id.id
            })
        maintain_id.write({
            'last_maintenance_date': (datetime.datetime.now() + datetime.timedelta(hours=8)).date()
        })

    # 获取时间标识
    def get_interval_type(self, res):
        if res.frequency_unit == '年':
            interval_number = res.frequency * 12
            interval_type = 'months'
        elif res.frequency_unit == '月':
            interval_number = res.frequency
            interval_type = 'months'
        elif res.frequency_unit == '周':
            interval_number = res.frequency
            interval_type = 'weeks'
        else:
            interval_number = res.frequency
            interval_type = 'days'
        return interval_number, interval_type

    @api.model
    def create(self, vals):
        res = super(RokeMoldMaintain, self).create(vals)
        # 自动生成定时任务
        interval_number, interval_type = self.get_interval_type(res)
        cron_id = self.sudo().env['ir.cron'].create({
            'name': '模具保养方案[名称：%s，周期：%s]' % (res.name, str(res.frequency) + res.frequency_unit),
            'model_id': self.sudo().env['ir.model'].search([('model', '=', 'roke.mold.maintain')],
                                                           limit=1).id,
            'state': 'code',
            'code': 'model.create_maintenance_order(%s)' % str(res.id),
            'interval_number': interval_number,
            'interval_type': interval_type,
            'numbercall': -1,
            'doall': True,
            'active': True
        })
        res.ir_cron = cron_id.id
        return res

    def unlink(self):
        # 同步删除定时任务
        for record in self:
            if record.ir_cron:
                record.ir_cron.unlink()
        res = super(RokeMoldMaintain, self).unlink()
        return res

    def write(self, vals):
        res = super(RokeMoldMaintain, self).write(vals)
        _logger.info(self.frequency_days)
        # 保养日期，默认禁止编辑，通过创建保养任务进行修改
        if (not vals.get('last_maintenance_date', '')) and (not vals.get('next_maintenance_date', '')):
            self.update_ir_cron()
        return res

    # 更新保养动作
    def update_ir_cron(self):
        for record in self:
            interval_number, interval_type = self.get_interval_type(record)
            if record.ir_cron:
                record.ir_cron.write({
                    'name': '模具保养方案[名称：%s，周期：%s]' % (
                        record.name, str(record.frequency) + record.frequency_unit),
                    'interval_number': interval_number,
                    'interval_type': interval_type
                })
            else:
                cron_id = self.sudo().env['ir.cron'].create({
                    'name': '模具保养方案[名称：%s，周期：%s]' % (
                        record.name, str(record.frequency) + record.frequency_unit),
                    'model_id': self.sudo().env['ir.model'].search(
                        [('model', '=', 'roke.mold.maintain')], limit=1).id,
                    'state': 'code',
                    'code': 'model.create_maintenance_order(%s)' % str(record.id),
                    'interval_number': interval_number,
                    'interval_type': interval_type,
                    'numbercall': -1,
                    'doall': True,
                    'active': True
                })
                record.ir_cron = cron_id.id

    def _compute_show_frequency(self):
        for record in self:
            record.show_frequency = "%s%s" % (str(record.frequency), record.frequency_unit)

    @api.depends("frequency_unit", "frequency")
    def _compute_frequency_days(self):
        # 计算保养频率间隔天数
        for record in self:
            if record.frequency_unit == "年":
                unit = 365
            elif record.frequency_unit == "月":
                unit = 30
            elif record.frequency_unit == "周":
                unit = 7
            else:
                unit = 1
            record.frequency_days = record.frequency * unit

    @api.depends("last_maintenance_date", "frequency_days", "ir_cron", "ir_cron.nextcall")
    def _compute_next_maintenance_date(self):
        # 计算下次保养日期
        for record in self:
            # 当前日期
            current = (datetime.datetime.now() + datetime.timedelta(hours=8)).date()
            if record.ir_cron:
                # 普通下次日期
                next_1 = (datetime.datetime.now() + datetime.timedelta(days=record.frequency_days) + datetime.timedelta(
                    hours=8)).date()
                # 定时任务下次日期
                next_2 = (record.ir_cron.nextcall + datetime.timedelta(hours=8)).date()
                record.next_maintenance_date = next_1 if next_2 == current else next_2
                record.last_maintenance_date = current
            else:
                record.next_maintenance_date = current
                record.last_maintenance_date = current

class RokeMoldMaintainRecords(models.Model):
    _name = "roke.mold.maintain.records"
    _description = "模具保养记录"
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _rec_name = "code"

    code = fields.Char(string="保养记录编号", tracking=True, required=True, copy=False,
                       default=lambda self: self.env['ir.sequence'].next_by_code('roke.mold.maintain.records.code'))
    maintain_id = fields.Many2one("roke.mold.maintain", string="保养方案", ondelete='cascade')
    mold_id = fields.Many2one("roke.mold.ledger", string="保养模具", required=True, ondelete='cascade')
    maintain_user = fields.Many2many("res.users", string="保养人", tracking=True)
    maintain_users = fields.Many2many("roke.employee", string="保养人员")
    maintain_apply_user = fields.Many2one("res.users", string="保养申请人", default=lambda self: self.env.user.id, tracking=True)
    state = fields.Selection(
        [("草稿", "草稿"), ("保养中", "保养中"), ("转维修", "转维修"), ("完成", "完成")], string="状态", default="草稿")
    workshop_id = fields.Many2one("roke.workshop", string="车间", tracking=True)
    maintain_type = fields.Selection(
        [("上机保养", "上机保养"), ("下机保养", "下机保养"), ("其他", "其他")], string="保养类型", default="上机保养")
    maintain_time = fields.Date(string="保养时间", default=fields.Date.context_today, tracking=True)
    is_maintain = fields.Boolean(string="是否保养", default=True, tracking=True)
    start_time = fields.Datetime(string="开始时间", tracking=True)
    end_time = fields.Datetime(string="结束时间", tracking=True)
    use_time = fields.Float(string="保养用时", store=True, compute="_compute_use_time")
    note = fields.Html(string="备注", tracking=True)
    picture = fields.Binary('图片')
    maintain_note = fields.Char(string="保养备注", tracking=True)
    result = fields.Char(string="保养结果", tracking=True)
    last_state = fields.Char(string="上一状态")  # 只起记录作用，无实际作用
    task_records_id = fields.Many2one("roke.production.task", string="生产任务id")
    plan_maintain_start_time = fields.Datetime(string="计划开始保养时间", tracking=True)
    plan_maintain_end_time = fields.Datetime(string="计划结束保养时间", tracking=True)
    priority = fields.Selection([('low', '低'), ('normal', '中'), ('high', '高'), ('urgent', '急')], string="紧急程度",
                                default="normal")
    deadline = fields.Date(string="最后期限")
    failure_id = fields.Many2one(string="故障类型", comodel_name="mold.failure", tracking=True)
    item_ids = fields.One2many(string="模具保养工单明细", comodel_name="mold.maintain.order.item", inverse_name="order_id")

    @api.onchange("maintain_id")
    def _onchange_maintenance_scheme_id(self):
        _ids_list = []
        for item in self.maintain_id.item_ids:
            _item = self.env['mold.maintain.order.item'].create({
                "item_id": item.id,
                "state": "wait",
            })
            _ids_list.append(_item.id)
        return {"value": {
            "item_ids": [(6, 0, _ids_list)]
        }}

    # 使用耗时
    @api.depends("start_time", "end_time")
    def _compute_use_time(self):
        for record in self:
            if record.start_time and record.end_time:
                record.use_time = (record.end_time - record.start_time).total_seconds() / 3600

    # 开始保养
    def start_action(self):
        self.write({
            'start_time': datetime.datetime.now(),
            'state': '保养中',
            'last_state': self.mold_id.state
        })
        self.mold_id.write({'state': '保养'})

    # 保养完成
    def confirm(self):
        self.state = "完成"
        self.end_time = datetime.datetime.now()

    # 指派-废除
    def assign(self):
        return {
            'name': '保养派工',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'target': 'new',
            'res_model': 'roke.maintain.wizard',
            'context': {'default_maintain_user': self.maintain_user.ids if self.maintain_user else False,
                        'default_maintain_id': self.id,
                        'default_type': '派工'}
        }

    # 生成维修单按钮方法
    def create_repair_orders(self):
        for record in self:
            repair_records = self.env['roke.mold.repair.records'].create({
                'mold_id': record.mold_id.id,
                # 'mold_code': record.code,
                'submit_user': record.maintain_apply_user.id,
                'repair_user': record.maintain_user[0].id if record.maintain_user else False,
                'start_repair_time': record.start_time,
                'source': record.code
                # 设置维护单其他字段的值
            })
            record.state = "转维修"
            return repair_records

    # 保养60天消息提醒
    def maintenance_reminders(self):
        records = self.env['roke.mold.maintain.records'].search(
            ['|', ('mold_id.state', '!=', '报废'), ('mold_id.state', '!=', '封存')])
        if self.is_maintain:
            for time in records:
                if datetime.datetime.today() - time.end_time >= timedelta(days=60):
                    self.env['roke.mold.maintain.records'].create([{
                        'mold_id': time.mold_id.id,
                        'maintain_id': time.maintain_id.id or False,
                        'maintain_user': time.maintain_user[0].id if time.maintain_user else False,
                        'maintain_apply_user': time.maintain_apply_user.id or False,
                        'workshop_id': time.workshop_id.id or False,
                        'maintain_time': time.maintain_time or '',
                        'use_time': time.use_time or '',
                        'maintain_type': time.maintain_type,
                        'is_maintain': time.is_maintain or '',
                        'result': time.result or '',
                        'maintain_note': time.maintain_note or '',
                        'note': time.note or '',
                    }])


class RokeMoldRepairRecords(models.Model):
    _name = "roke.mold.repair.records"
    _description = "模具维修单"
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _rec_name = "code"
    _order = "id desc"

    code = fields.Char(string="维修记录编号", tracking=True, required=True, copy=False,
                       default=lambda self: self.env['ir.sequence'].next_by_code('roke.mold.repair.records.code'))
    mold_id = fields.Many2one("roke.mold.ledger", string="模具名称", required=True, ondelete='cascade')
    mold_code = fields.Char(string="模具编号", related="mold_id.code")
    cavities_number = fields.Integer(string="模具腔数", related="mold_id.cavities_number")
    repair_origin = fields.Selection([('日常报修', '日常报修'), ('维保报修', '维保报修'), ('班检报修', '班检报修'), ('模貝委外报修', '模貝委外报修')], string="报修来源", default='日常报修', tracking=True)
    repair_user = fields.Many2one("res.users", string="维修人", tracking=True)
    repair_users = fields.Many2many("res.users", string="维修人", tracking=True)
    submit_user = fields.Many2one("res.users", string="报修人", tracking=True, default=lambda self: self.env.user.id)
    start_repair_time = fields.Date(string="报修日期", default=fields.Date.context_today, tracking=True)
    finish_repair_time = fields.Datetime(string="维修完成时间", tracking=True)
    note = fields.Text(string="故障描述", tracking=True)
    result = fields.Text(string="处理结果", tracking=True)
    state = fields.Selection([('草稿', '草稿'), ('维修中', '维修中'), ('完成', '完成'), ('委外', '委外')], string="状态", default='草稿', tracking=True)
    repair_state = fields.Selection([('车间模具维修单', '车间模具维修单'), ('模具中心维修单', '模具中心维修单')], string="维修单类型", default='车间模具维修单', tracking=True)
    start_time = fields.Datetime(string="开始时间", tracking=True)
    end_time = fields.Datetime(string="结束时间", tracking=True)
    use_time = fields.Float(string="维修用时", store=True, compute="_compute_use_time")
    fault_picture = fields.Binary('故障图片')
    repair_picture = fields.Binary('维修图片')
    last_state = fields.Char(string="上一状态")  # 只起记录作用，无实际作用
    source = fields.Char(string="来源")
    failure_id = fields.Many2one(string="报修故障类型", comodel_name="mold.failure", tracking=True)
    deadline = fields.Date(string="最后期限")
    priority = fields.Selection([('low', '低'), ('normal', '中'), ('high', '高'), ('urgent', '急')], string="紧急程度",
                                default="normal")
    team_leader_id = fields.Many2one("res.users", string="班组长", tracking=True)
    repair_failure_id = fields.Many2one("mold.failure", string="维修故障类型", tracking=True)
    m_location = fields.Char(string="当前位置")


    def action_outsourcing(self):
        self.state = "委外"

    def outsourcing_end(self):
        self.mold_id.state = "闲置"
        self.state = "完成"

    def judge_classes(self):
        classes_id = self.env["roke.classes"].search([("name", "=", "白班")], limit=1)
        start_time = classes_id.start_time
        start_minute, start_hour = math.modf(start_time)
        end_time = classes_id.end_time
        end_minute, end_hour = math.modf(end_time)
        utc_now = fields.Datetime.now()
        repair_team_schedule_id = self.env["repair.team.schedule"].search(
            [("type", "=", "mold"), ("start_date", "<=", utc_now), ("end_date", ">=", utc_now)], limit=1)
        if not repair_team_schedule_id:
            return
        now = utc_now + datetime.timedelta(hours=8)
        if start_hour < now.hour < end_hour or start_hour == now.hour and start_minute <= now.minute or now.hour == end_hour and now.minute <= end_minute:
            self.repair_user = False
        else:
            schedule_line_ids = repair_team_schedule_id.schedule_line_ids.filtered(
                lambda x: x.classes_id.name == "夜班")
            if schedule_line_ids:
                self.repair_user = schedule_line_ids[0].user_id.id
            else:
                self.repair_user = False
        self.repair_user = False
        self.notice_create()

    @api.model
    def create(self, vals):
        res = super(RokeMoldRepairRecords, self).create(vals)
        res.judge_classes()
        return res

    def notice_create(self):
        _logger.error("模具维修单发送消息！")

    def write(self, vals):
        res = super(RokeMoldRepairRecords, self).write(vals)
        if vals.get("repair_user"):
            self.notice_create()
        return res

    # 使用耗时
    @api.depends("start_time", "end_time")
    def _compute_use_time(self):
        for record in self:
            if record.start_time and record.end_time:
                record.use_time = (record.end_time - record.start_time).total_seconds() / 3600

    # 开始维修
    def start_action(self):
        self.write({
            'start_time': fields.Datetime.now(),
            'state': '维修中',
            'repair_user': self.env.user.id
        })
        if self.mold_id.state != "维修":
            self.last_state = self.mold_id.state
            self.mold_id.write({'state': '维修'})

    # 维修完成
    def confirm_repair(self):
        self.write({
            'end_time': fields.Datetime.now(),
            'state': '完成',
            'finish_repair_time': fields.Datetime.now()
        })
        if self.source:
            self.mold_id.write({'state': '闲置'})
        else:
            self.mold_id.write({'state': self.last_state or '闲置'})

    def outsourcing_end(self):
        self.mold_id.state = "闲置"
        self.state = "完成"

class RokeMoldPickRecords(models.Model):
    _name = "roke.mold.pick.records"
    _description = "数采记录"
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _rec_name = "mold_id"

    mold_id = fields.Many2one("roke.mold.ledger", string="模具台账", required=True, ondelete='cascade')
    pick_time = fields.Datetime(string="采集时间", default=fields.Datetime.now(), tracking=True)
    start_pick_data = fields.Integer(string="开始开合次数", tracking=True)
    stop_pick_data = fields.Integer(string="结束开合次数", tracking=True)
    pick_data = fields.Integer(string="使用开合次数", tracking=True)
    start_high_pick_data = fields.Integer(string="开始累计产量-高位")
    start_low_pick_data = fields.Integer(string="开始累计产量-低位")
    stop_high_pick_data = fields.Integer(string="结束累计产量-高位")
    stop_low_pick_data = fields.Integer(string="结束累计产量-低位")

    note = fields.Text(string="备注", tracking=True)


class RokeMoldUseRecords(models.Model):
    _name = "roke.mold.use.records"
    _description = "模具领用"
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _rec_name = "equipment_id"

    line_id = fields.Many2one("roke.mold.ledger", string="模具", required=True, ondelete='cascade')
    code = fields.Char(string="模具领用编号", tracking=True, copy=False,
                       default='/')
    workshop_id = fields.Many2one("roke.workshop", string="领用车间", tracking=True)
    equipment_id = fields.Many2one("roke.mes.equipment", string="设备", tracking=True)
    # acceptance_user = fields.Many2one("res.users", string="领用人", tracking=True, default=lambda s: s.env.uid)
    acceptance_user_ids = fields.Many2many("res.users", string="领用人", tracking=True, default=lambda self: self.env.user)
    mold_location_id = fields.Many2one("roke.mes.stock.location", string="模具位置", domain="[('is_hardware', '=', True)]",
                                       tracking=True)
    location_id = fields.Many2one("roke.mold.location", string="领用库位", tracking=True)
    location = fields.Char(string="领用库位", tracking=True)
    acceptance_time = fields.Datetime(string="领用日期", tracking=True)
    return_time = fields.Datetime(string="归还日期", tracking=True)
    note = fields.Text(string="领用备注", tracking=True)
    use_note = fields.Char(string="申请事由", tracking=True)
    type = fields.Selection([('领用', '领用'), ('归还', '归还')], string="状态", default='领用', tracking=True)
    use_id = fields.Many2one("roke.mold.use.records", string="领用记录")
    use_start_time = fields.Char(string="领用开始时间", tracking=True)
    use_end_time = fields.Char(string="领用结束时间", tracking=True)
    return_start_time = fields.Char(string="归还开始时间", tracking=True)
    return_end_time = fields.Char(string="归还结束时间", tracking=True)

    @api.model
    def default_get(self, fields):
        res = super(RokeMoldUseRecords, self).default_get(fields)
        if res.get('type', '') == '领用':
            res.update({'acceptance_time': datetime.datetime.today()})
        if res.get('type', '') == '归还':
            res.update({'return_time': datetime.datetime.today()})
        return res

    @api.model
    def create(self, vals):
        if vals.get('type') == '领用':
            vals["code"] = self.env['ir.sequence'].next_by_code('roke.mold.use.records.code')
        else:
            vals["code"] = self.env['ir.sequence'].next_by_code('roke.mold.return.records.code')
        res = super(RokeMoldUseRecords, self).create(vals)
        # 归还填写时间，赋予领用归还时间，设备解绑     库位为空
        if res.type == '归还':
            # res.line_id.location_id = False
            if not res.return_time:
                res.return_time = datetime.datetime.today()
            # 设备解绑
            if res.equipment_id:
                res.equipment_id.mold_id = False
                res.line_id.equipment_id = False
            # 车间解绑
            workshop_id = res.workshop_id.id
            # if res.workshop_id:
            #     res.line_id.workshop_id = False
            if res.use_id:
                res.use_id.return_time = datetime.datetime.today()
            # else:
            #     raise ValidationError('归还单存在问题，请重新创建！')
            res.line_id.state = '闲置'
            # 归还后，创建保养任务
            # self.env['roke.mold.maintain.records'].create({
            #     'mold_id': res.line_id.id,
            #     'state': '草稿',
            #     'note': '归还后保养',
            #     'maintain_type': '下机保养',
            #     'workshop_id': workshop_id
            # })
        # 领用绑定设备
        if res.type == '领用':
            res.line_id.state = '在产'
            # 设备绑定
            if res.equipment_id:
                res.equipment_id.mold_id = res.line_id.id
                res.line_id.equipment_id = res.equipment_id.id
            # 车间绑定
            if res.workshop_id:
                res.line_id.workshop_id = res.workshop_id.id
            # 库位绑定
            if res.location:
                res.line_id.location = res.location if res.location else ''
        return res

    @api.onchange("line_id")
    def _onchange_line_id(self):
        """
        监听归还
        :return:
        """
        # 领用判断是否被再次领用
        if self.line_id and self.type == '领用':
            # 该模具、已领用、未归还
            record = self.env['roke.mold.use.records'].search(
                [('line_id', '=' , self.line_id.id), ('type', '=', '领用'), ('return_time', '=', False)], limit=1
            )
            if record:
                user_names = ", ".join(record.acceptance_user_ids.mapped("name"))
                raise ValidationError(
                    '模具【%s】已在【%s】被【%s】领用，绑定与设备【%s】'
                    # % (record.line_id.name, record.acceptance_time, record.acceptance_user.name, record.equipment_id.name))
                    % (record.line_id.name, record.acceptance_time, user_names, record.equipment_id.name))
        # 归还判断是否被未被领用
        if self.line_id and self.type == '归还':
            # 该模具、从未领用
            ly_record = self.env['roke.mold.use.records'].search(
                [('line_id', '=', self.line_id.id), ('type', '=', '领用')], limit=1
            )
            # 该模具，领用了，全部归还
            gh_record = self.env['roke.mold.use.records'].search(
                [('line_id', '=', self.line_id.id), ('type', '=', '领用'), ('return_time', '=', False)]
            )
            if not ly_record or not gh_record:
                raise ValidationError('模具【%s】未被领用，无需归还！' % self.line_id.name)
            # 填充解绑设备
            record = self.env['roke.mold.use.records'].search(
                [('line_id', '=', self.line_id.id), ('type', '=', '领用'), ('return_time', '=', False)], limit=1
            )
            if record:
                self.equipment_id = record.equipment_id.id
                self.use_id = record.id

    # 获取token
    def get_token(self):
        get_token = requests.post(
                        url="http://*************:8001/api/login/",
                        data={"username": "feifanadmin", "password": "admin123123"})
        if get_token.status_code == 200:
            get_token = json.loads(get_token.text)
            erp_token = "JWT " + get_token['data']['access']
            return erp_token
        else:
            raise ValidationError('调用第三方接口失败!状态码:[%s]' % (get_token.get_token))

    # 获取领用数采记录
    def get_start_mold_pick_records(self, erp_id, line_id):
        erp_token = self.get_token()
        res = requests.get("http://*************:8001/api/project/acquire_product_device_get/",
                           params={"device_id" : str(erp_id)},
                           headers={"Authorization": erp_token})
        if res.status_code == 200:
            res = json.loads(res.text)

            start_pick_data = res['data']['production_sum']
            start_high_pick_data = res['data']['production_high']
            start_low_pick_data = res['data']['production_low']
            collect_time = datetime.datetime.strptime(res['data']['collect_time'], "%Y-%m-%d %H:%M:%S") + datetime.timedelta(hours=-8)

            self.env['roke.mold.pick.records'].create({
                'mold_id': line_id,
                'pick_time':collect_time,
                'start_pick_data': start_pick_data,
                'start_high_pick_data': start_high_pick_data,
                'start_low_pick_data': start_low_pick_data,
            })
        else:
            raise ValidationError('调用第三方接口失败!状态码:[%s]' % (res.status_code))

    # 获取归还数采记录
    def get_stop_mold_pick_records(self, erp_id ,line_id):
        erp_token = self.get_token()
        res = requests.get('http://*************:8001/api/project/acquire_product_device_get/',
                           params={"device_id" : str(erp_id)},
                           headers={"Authorization": erp_token})
        if res.status_code == 200:
            res = json.loads(res.text)

            stop_pick_data = res['data']['production_sum']
            stop_high_pick_data = res['data']['production_high']
            stop_low_pick_data = res['data']['production_low']

            mold_pick = self.env['roke.mold.pick.records'].search([('mold_id', '=', line_id)],limit=1,
                                                                  order='create_date desc')
            if mold_pick:
                pick_data = stop_pick_data - mold_pick.start_pick_data
                mold_pick.write({
                    'stop_pick_data' : stop_pick_data,
                    'stop_high_pick_data': stop_high_pick_data,
                    'stop_low_pick_data': stop_low_pick_data,
                    'pick_data': pick_data
                })
        else:
            raise ValidationError('调用第三方接口失败!状态码:[%s]' % (res.status_code))

# 模具仓库
class RokeMoldlocation(models.Model):
    _name = "roke.mold.location"
    _description = "模具仓库"
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _rec_name = "name"

    name = fields.Char(string="仓库名称", tracking=True, required=True)
    code = fields.Char(string="仓库编号", tracking=True, required=True, copy=False,
                       default=lambda self: self.env['ir.sequence'].next_by_code('roke.mold.location.code'))
    note = fields.Text(string="备注", tracking=True)
    type = fields.Selection([('仓库', '仓库'), ('库区', '库区'), ('库位', '库位')], string="仓库类型", default='库位')

    _sql_constraints = [
        ('code_unique', 'UNIQUE(code)', '编号已存在，不可重复。如使用系统默认编号请刷新页面；如使用自定义编号请先删除重复的编号')
    ]

class SjzzhInheritRokeMesEquipment(models.Model):
    _inherit = "roke.mes.equipment"
    _order = 'code_sort asc'

    mold_id = fields.Many2one("roke.mold.ledger", string="当前绑定模具", tracking=True)
    code_sort = fields.Char(string="设备编号排序", compute="_compute_code_sort", store=True)

    @api.depends("code")
    def _compute_code_sort(self):
        for record in self:
            if record.code:
                if record.code.split('-')[-1].isdigit():
                    record.code_sort = str(int(record.code.split('-')[-1])).zfill(5)
                else:
                    sum_str = record.code.split('-')[-1]
                    if ''.join(filter(str.isdigit, sum_str)):
                        str_num = ''.join(filter(str.isdigit, sum_str))
                        record.code_sort = sum_str.replace(str_num, '') + str(int(str_num)).zfill(5)
                    else:
                        record.code_sort = 0
            else:
                record.code_sort = 0


class RokeMoldScrappingRequest(models.Model):
    _name = "roke.mold.scrapping.request"
    _description = "报废申请单"
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _rec_name = "code"

    mold_id = fields.Many2one("roke.mold.ledger", string="模具", required=True)
    code = fields.Char(string="报废申请单编号", tracking=True, required=True, copy=False,
                       default=lambda self: self.env['ir.sequence'].next_by_code('roke.mold.scrapping.request.code'))
    specification = fields.Char(string="规格", related="mold_id.specification")
    mold_out_code = fields.Char(string="模具出厂编号", related="mold_id.specification")
    category_id = fields.Many2one(string="模具类别", comodel_name="mold.category", related="mold_id.category_id")
    cavities_number = fields.Integer(string="模具腔数、数量", related="mold_id.cavities_number")
    apply_user = fields.Many2one("res.users", string="申请人", tracking=True, default=lambda s: s.env.uid)
    apply_date = fields.Date(string="领用日期", tracking=True, default=fields.Date.context_today)
    use_department = fields.Many2one('roke.department', string="使用部门", tracking=True)
    problem_description = fields.Text(string="问题描述", tracking=True)
    result = fields.Char(string="论证结果", tracking=True)
    product_header = fields.Char(string="产品技术负责人", tracking=True)
    use_factory = fields.Char(string="使用分厂", tracking=True)
    sale_company = fields.Char(string="销售公司", tracking=True)
    production_department = fields.Char(string="生产部", tracking=True)
    technology_center = fields.Char(string="技术中心", tracking=True)
    finance_department = fields.Char(string="财务部", tracking=True)
    manager_approval = fields.Char(string="总经理审批", tracking=True)
    state = fields.Selection([('草稿', '草稿'), ('审批通过', '审批通过'), ('审批未通过', '审批未通过')],
                             string="状态", default='草稿', tracking=True)

    def action_approve(self):
        """
        审批通过
        :return:
        """
        self.state = '审批通过'
        self.mold_id.state = '报废'

    def action_turn_down(self):
        """
        审批驳回
        :return:
        """
        self.state = '审批未通过'

    def unlink(self):
        return super(RokeMoldScrappingRequest, self).unlink()

    def write(self, vals):
        return super(RokeMoldScrappingRequest, self).write(vals)


class RokeMoldSealedRequest(models.Model):
    _name = "roke.mold.sealed.request"
    _description = "封存申请单"
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _rec_name = "code"

    mold_id = fields.Many2one("roke.mold.ledger", string="模具", required=True)
    code = fields.Char(string="封存申请单编号", tracking=True, required=True, copy=False,
                       default=lambda self: self.env['ir.sequence'].next_by_code('roke.mold.sealed.request.code'))
    specification = fields.Char(string="规格", related="mold_id.specification")
    mold_out_code = fields.Char(string="模具出厂编号", related="mold_id.specification")
    category_id = fields.Many2one(string="模具类别", comodel_name="mold.category", related="mold_id.category_id")
    cavities_number = fields.Integer(string="模具腔数、数量", related="mold_id.cavities_number")
    apply_user = fields.Many2one("res.users", string="申请人", tracking=True, default=lambda s: s.env.uid)
    apply_date = fields.Date(string="领用日期", tracking=True, default=fields.Date.context_today)
    use_department = fields.Many2one('roke.department', string="使用部门", tracking=True)
    problem_description = fields.Text(string="问题描述", tracking=True)
    result = fields.Char(string="论证结果", tracking=True)
    product_header = fields.Char(string="产品技术负责人", tracking=True)
    use_factory = fields.Char(string="使用分厂", tracking=True)
    sale_company = fields.Char(string="销售公司", tracking=True)
    production_department = fields.Char(string="生产部", tracking=True)
    technology_center = fields.Char(string="技术中心", tracking=True)
    finance_department = fields.Char(string="财务部", tracking=True)
    manager_approval = fields.Char(string="总经理审批", tracking=True)
    state = fields.Selection([('草稿', '草稿'), ('审批通过', '审批通过'), ('审批未通过', '审批未通过')],
                             string="状态", default='草稿', tracking=True)

    def action_approve(self):
        """
        审批通过
        :return:
        """
        self.state = '审批通过'
        self.mold_id.state = '封存'

    def action_turn_down(self):
        """
        审批驳回
        :return:
        """
        self.state = '审批未通过'

    def unlink(self):
        return super(RokeMoldSealedRequest, self).unlink()

    def write(self, vals):
        return super(RokeMoldSealedRequest, self).write(vals)
