# -*- coding: utf-8 -*-
"""
Description:
    产出物
Versions:
    Created by www.inspur.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class InheritProductionResult(models.Model):
    _inherit = "roke.production.result"

    pr_stock_move_id = fields.Many2one("roke.mes.stock.move", string="产出库存移动", index=True)
    location_id = fields.Many2one("roke.mes.stock.location", string="产出存放位置")

    def generate_pr_stock_move_line_vals(self, dest_location_id, lot_id):
        """
        获取产出物生成时的stock_move_line
        :return:
        """
        return {
            'dest_location_id': dest_location_id.id,
            "product_id": self.product_id.id,
            "product_state_id": self.wr_id.product_state_id.id,
            "qty": self.qty,
            "lot_id": lot_id
        }

    def generate_pr_stock_move_vals(self, dest_location, lot_id):
        """
        获取产出物生成时的stock_move
        :return:
        """
        line_vals = [(0, 0, self.generate_pr_stock_move_line_vals(dest_location, lot_id))]
        return {
            "dest_location_id": dest_location.id,
            "product_id": self.product_id.id,
            "qty": self.qty,
            "move_date": fields.Date.context_today(self),
            "line_ids": line_vals
        }

    # 获取批次号创建信息
    def _get_lot_data(self):
        return {
            "name": self.lot_code,
            "product_id": self.product_id.id,
            "partner_id": self.customer_id.id
        }

    def generate_pr_stock_move(self, dest_location):
        """
        根据产出物记录生成库存移动
        :param dest_location:
        :return:
        """
        self.ensure_one()
        lot_id = False
        if self.lot_code and self.product_id.track_type != 'none':
            lot_id = self.env["roke.mes.stock.lot"].search([("name", "=", self.lot_code)], limit=1).id
            if not lot_id:
                _logger.info('创建批次')
                lot_id = self.env["roke.mes.stock.lot"].create(self._get_lot_data()).id
        stock_move = self.env["roke.mes.stock.move"].create(self.generate_pr_stock_move_vals(dest_location, lot_id))
        self.write({"pr_stock_move_id": stock_move.id, "location_id": dest_location.id})
        try:
            # 完成移动
            stock_move.action_done()
        except Exception as e:
            stock_move.unlink()
            raise ValidationError("无法完成产出移动")

    def get_container_record_with_location(self):
        if self.wr_id.container_code:
            location_id = self.env["roke.mes.stock.location"].sudo().search([('is_container','=',True),
                                                                           ("container_code", "=", self.wr_id.container_code)],limit=1)
            if location_id:
                return location_id
        container_record = self.env['roke.container.record'].sudo().search([
            ('work_order_id', '=', self.wo_id.id)
        ], order='id desc', limit=1)
        if container_record:
            location_id = self.env["roke.mes.stock.location"].sudo().search([('is_container','=',True),
                                                                           ("container_code", "=", container_record.code)],limit=1)
            if location_id:
                return location_id
        return False
    def get_production_result_location(self):
        """
        获取产出物位置
        :return:
        """
        self.ensure_one()
        if self.wr_id.work_center_id.stock_location_id:
            return self.wr_id.work_center_id.stock_location_id
        elif self.wo_id.routing_line_id.work_center_ids:
            return self.wo_id.routing_line_id.work_center_ids[0].work_center_id.stock_location_id
        elif self.wo_id.process_id.work_center_ids:
            return self.wo_id.process_id.work_center_ids[0].work_center_id.stock_location_id
        else:
            return False

    def action_auto_deduction_pr(self):
        """
        产出时自动扣料
        :return:
        """
        PickMoveAll = self.sudo().env["roke.wizard.mes.picking.all.move"]
        for record in self:
            work_order = record.wo_id
            routing = work_order.routing_line_id.routing_id
            # 判断是否执行扣料；副产品不扣料
            if routing.deduction_moment == "末道扣料" and record.result_type != "副产品":
                # 执行扣料
                deduction_pickings = record.wr_id.execute_auto_deduction(work_order, record.qty)
                # 报工记录关联扣料单
                deduction_stock_picking_ids = deduction_pickings.ids + record.wr_id.deduction_stock_picking_ids.ids
                record.wr_id.write({"deduction_stock_picking_ids": [(6, 0, deduction_stock_picking_ids)]})
                record.env.cr.commit()
                # 自动完成扣料
                if routing.deduction_auto_confirm:
                    for stock_picking in deduction_pickings:
                        try:
                            finish_result = stock_picking.button_finish()
                            if finish_result:
                                PickMoveAll.create({"picking_id": stock_picking.id}).confirm()
                                stock_picking.action_assign()
                                stock_picking.make_finish()
                        except Exception as e:
                            _logger.error(e)

    def _auto_stock_get_lines(self, result_res):
        """
        自动入库获取入库明细
        :return:
        """
        auto_stock_location = self.env['ir.config_parameter'].sudo().get_param('production.auto.stock.location',
                                                                               default="产品默认仓库")
        if auto_stock_location == "任务工作中心位置":
            dest_location = result_res.pt_id.work_center_id.stock_location_id
        else:
            dest_location = result_res.product_id.default_product_warehouse

        location_id = self.get_container_record_with_location()
        if location_id:
            dest_location = location_id

        return {
            "result_id": result_res.id,
            "product_state_id": result_res.product_state_id.id,
            "wait_qty": result_res.residue,
            "stock_qty": result_res.residue,
            "dest_location_id": dest_location.id
        }

    @api.model
    def create(self, vals):
        res = super(InheritProductionResult, self).create(vals)
        # 自动扣料
        res.action_auto_deduction_pr()
        # 产出物入库到当前工序的线边库
        for record in res:
            dest_location = record.get_production_result_location()
            if dest_location:
                record.generate_pr_stock_move(dest_location)
        # 自动入库
        production_stock_type = self.env['ir.config_parameter'].sudo().get_param('production.stock.type', default="手动")
        picking_stock_type = self.env['ir.config_parameter'].sudo().get_param('picking.stock.type', default="创建并入库")
        if production_stock_type != "手动":
            line_list = []
            for result_res in res:
                line_list.append(
                    (0, 0, self._auto_stock_get_lines(result_res))
                )
            self.env["roke.result.put.warehouse.wizard"].create({
                "line_ids": line_list
            }).confirm()
            # 自动完成入库
            if picking_stock_type == '创建并入库':
                for picking in res.line_ids.picking_id:
                    picking.make_finish()
        return res

    def withdraw(self):
        """
        撤回是删除仓库入库单
        :return:
        """
        self.pr_stock_move_id.action_cancel()  # 取消move
        self.pr_stock_move_id.line_ids.unlink()  # 删除move.line更新账本
        self.pr_stock_move_id.unlink()  # 删除move
        return super(InheritProductionResult, self).withdraw()

    def unlink(self):
        """
        撤销报工时会删掉当前产出记录，删除时要级联取消自动扣料的内容
        :return:
        """
        for record in self:
            record.wr_id.cancel_deduction()
        return super(InheritProductionResult, self).unlink()
