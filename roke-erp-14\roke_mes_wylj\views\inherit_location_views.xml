<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_wylj_inherit_view_roke_mes_stock_location_form" model="ir.ui.view">
        <field name="name">view_wylj_inherit_view_roke_mes_stock_location_form</field>
        <field name="model">roke.mes.stock.location</field>
        <field name="inherit_id" ref="roke_mes_stock.view_roke_mes_stock_location_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='production_location']" position="after">
                <field name="is_hardware"/>
                <field name="is_container"/>
            </xpath>
            <xpath expr="//field[@name='is_minus']" position="after">
                <field name="roke_company_id"/>
                <field name="container_code" attrs="{'required':[('is_container','=',True)]}"/>
            </xpath>
            <xpath expr="//field[@name='parent_id']" position="attributes">
                <attribute name="domain">[]</attribute>
            </xpath>
        </field>
    </record>
</odoo>