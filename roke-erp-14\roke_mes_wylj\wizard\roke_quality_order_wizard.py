from odoo import models, fields, api

class RokeQualityOrderWizard(models.TransientModel):
    _name = 'roke.quality.order.wizard'
    _description = '质检处理单创建质检单向导'

    processing_id = fields.Many2one('roke.quality.processing', string='质检处理单')
    line_ids = fields.One2many('roke.quality.order.wizard.line', 'wizard_id', string='质检单明细')

    def get_scheme_id(self, quality_type, product_id):
        if quality_type == '巡检':
            return product_id.atrol_scheme_id.id
        elif quality_type == '到货检':
            return product_id.arrival_scheme_id.id
        elif quality_type == '发货验':
            return product_id.shipment_scheme_id.id
        else:
            return False

    def confirm(self):
        quality_order_obj = self.env["roke.quality.order"]
        quantity = 0
        for line in self.line_ids:
            quality_order_vals = {
                "scheme_id": self.get_scheme_id(self.processing_id.quality_order_id.quality_type, line.processing_line_id.product_id),
                "product_id": line.processing_line_id.product_id.id,
                "plan_qty": line.unqualified_qty,
                "quality_type": self.processing_id.quality_order_id.quality_type,
            }
            quality_order_obj.create(quality_order_vals)
            quantity += line.unqualified_qty
            line.processing_line_id.unqualified_qty =  line.processing_line_id.unqualified_qty - line.unqualified_qty
        self.processing_id.quantity = quantity


class RokeQualityOrderWizardLine(models.TransientModel):
    _name = 'roke.quality.order.wizard.line'
    _description = '质检处理单明细'

    wizard_id = fields.Many2one('roke.quality.order.wizard', string='质检处理单向导')
    processing_line_id = fields.Many2one('roke.quality.processing.line', string='质检处理单明细')
    product_id = fields.Many2one(related="processing_line_id.product_id", string='产品', store=True)
    unqualified_qty = fields.Float(string='不合格数量')


