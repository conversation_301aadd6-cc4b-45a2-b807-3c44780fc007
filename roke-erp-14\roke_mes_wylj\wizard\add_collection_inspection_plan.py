# -*- coding: utf-8 -*-
"""
Description:
    批量添加工序
Versions:
    Created by www.inspur.com<HuChuanwei>
"""
from odoo import models, fields, api, _


class RokeWizardMultiAddProcess(models.TransientModel):
    _name = "add.collection.inspection.plan"

    equipment_ids = fields.Many2many("roke.mes.equipment.item",  string="选择数采")
    line_ids = fields.One2many("add.collection.inspection.plan.line", "wizard_id", string="选择")

    @api.onchange("equipment_ids")
    def _onchange_equipment_ids(self):
        """
        当选择设备项变化时，更新明细行
        """
        lines = []
        for line in self.line_ids:
            lines.append((2, line.id))
        for equipment in self.equipment_ids.ids:
            lines.append((0, 0, {
                "line_id": equipment,
            }))
        self.line_ids = lines



    def confirm(self):
        """
        根据序号创建工艺明细
        :return:
        """
        active_id = self._context.get('default_plan_id')
        if not active_id:
            return {'type': 'ir.actions.act_window_close'}

        plan = self.env['roke.data.collection.inspection.plan'].browse(active_id)
        lines = []
        for line in self.line_ids:
            lines.append((0, 0, {
                'check_item': line.line_id.id,
                'item_id': line.line_id.item_id
            }))
        if lines:
            plan.write({'line_ids': lines})

        return {'type': 'ir.actions.act_window_close'}


class RokeWizardMultiAddProcessLine(models.TransientModel):
    _name = "add.collection.inspection.plan.line"

    wizard_id = fields.Many2one("add.collection.inspection.plan", string="选择")
    line_id = fields.Many2one("roke.mes.equipment.item", string="点检项")
    item_id = fields.Char(string='检查项id', related='line_id.item_id', store=True)
