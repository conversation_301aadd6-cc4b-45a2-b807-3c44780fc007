from odoo import api, fields, models


class InheritRokeQualityOrder(models.Model):
    _inherit = 'roke.quality.order'

    quality_type = fields.Selection([
        ("发货检", "发货检"),
        ("到货检", "到货检"),
        ("巡检", "巡检"),
    ], string="质检类型", default="巡检")
    task_id = fields.Many2one('roke.production.task', string='生产任务')
    purchase_order_id = fields.Many2one('roke.mes.stock.picking', string='采购单', domain=[('type', '=', '入库')])
    stock_picking_id = fields.Many2one('roke.mes.stock.picking', string='发货单', domain=[('type', '=', '出库')])
    workshop_id = fields.Many2one('roke.workshop', string='车间')
    pass_qty = fields.Float(string="合格数量", digits='SCSL', compute='_compute_pass_fail_qty', store=True)
    fail_qty = fields.Float(string="不合格数量", digits='SCSL', compute='_compute_pass_fail_qty', store=True)

    @api.depends('line_ids.qty')
    def _compute_pass_fail_qty(self):
        for rec in self:
            rec.pass_qty = rec.plan_qty - sum(rec.line_ids.mapped('qty'))
            rec.fail_qty = sum(rec.line_ids.mapped('qty'))

    @api.model
    def create(self, vals):
        if vals.get("quality_type"):
            # 不同类型不同编号
            vals["code"] = self.env["ir.sequence"].next_by_code("roke.quality.order.%s.code" % vals.get("quality_type"))
        return super(InheritRokeQualityOrder, self).create(vals)


class InheritRokeQualityScheme(models.Model):
    _inherit = 'roke.quality.scheme'

    type = fields.Selection([("巡检", "巡检"), ("发货检", "发货检"), ("到货检", "到货检")], string="类型")


class InheritRokeQualityOrderLine(models.Model):
    _inherit = 'roke.quality.order.line'

    qty = fields.Float(string='不合格数量')
