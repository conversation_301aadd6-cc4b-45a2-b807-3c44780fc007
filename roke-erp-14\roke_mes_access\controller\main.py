# -*- coding: utf-8 -*-

from odoo import http
from odoo import SUPERUSER_ID
# from odoo.addons.roke_mes_production.controller.production import Production
from odoo.addons.roke_pub_access.controller.main import Access,generate_menu
from odoo.addons.roke_mes_access.models.inherit_res_groups import TOP_MENUS_LIST

headers = [('Content-Type', 'application/json; charset=utf-8')]

class InheritProduction(Access):

    @http.route("/roke/pub/get_menu_model_buttons", type='json', auth="none", cors='*', csrf=False)
    def get_menu_model_buttons(self, **kwargs):
        menu_id = kwargs.get("menu_id", False)
        role_id = kwargs.get("role_id", False)
        menu_obj = http.request.env(user=SUPERUSER_ID)['ir.ui.menu'].search([('id', '=', menu_id)])
        role_obj = http.request.env(user=SUPERUSER_ID)['res.groups'].search([('id', '=', role_id)])
        data = []
        try:
            res_model = menu_obj.action.res_model
            view_id = menu_obj.action.view_ids.filtered(lambda v: v.view_mode == 'form' and v.view_id.mode == 'primary')
            if view_id:
                model_buttons = http.request.env(user=SUPERUSER_ID)['roke.ir.ui.button'].search([
                    ("model", "=", res_model), ("view_id", "=", view_id[0].view_id.id)
                ])
            else:
                model_buttons = http.request.env(user=SUPERUSER_ID)['roke.ir.ui.button'].search([
                    ("model", "=", res_model)
                ])
            for item in model_buttons:
                data.append({
                    "id": item.id, "name": item.name, "model": item.model,
                    "active": item.id in role_obj.ir_button_ids.ids,
                })
        except Exception as ex:
            data = []
            return {"state": "error", "msgs": ex, "result": data}
        return {"state": "success", "msgs": "获取成功", "result": data}

    @http.route('/roke/pub/get_role_list', type='json', auth="none", cors='*', csrf=False)
    def get_role_list(self):
        """
        获取角色列表
        """
        try:
            role_obj = http.request.env(user=SUPERUSER_ID)['res.groups'].search([])
            # role_obj = http.request.env(user=SUPERUSER_ID)['res.groups'].get_show_roles()
            role_list = []
            for role in role_obj:
                role_list.append({
                    "id": role.id, "name": role.name
                })
            return {"state": "success", "msgs": "获取成功", "result": role_list}
        except Exception as e:
            return {"state": "error", "msgs": "获取失败", "result": e}