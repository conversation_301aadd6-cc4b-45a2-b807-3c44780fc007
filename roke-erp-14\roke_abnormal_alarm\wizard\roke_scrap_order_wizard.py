# -*- coding: utf-8 -*-
"""
Description:
    报废单
Versions:
    Created by www.inspur.com
"""
from odoo import models, fields, api, _
from odoo.exceptions import UserError

class RokeScrapOrderWizard(models.TransientModel):
    _name = "roke.scrap.order.wizard"
    _description = "报废单"

    alarm_id = fields.Many2one("roke.abnormal.alarm",string="告警记录")
    code = fields.Char(string="编号", required=True, index=True, copy=False,
                       default=lambda self: self.env['ir.sequence'].next_by_code('roke.scrap.order.code'))
    state = fields.Selection([("待确认", "待确认"), ("确认", "确认"), ("部分处理", "部分处理"), ("处理完成", "处理完成"), ("取消", "取消")],
                             required=True, default="待确认", string="状态")
    wr_id = fields.Many2one("roke.work.record", string="报工记录")
    wo_id = fields.Many2one(related="wr_id.work_order_id", string="工单", store=True)
    pt_id = fields.Many2one(related="wo_id.task_id", string="生产任务", store=True)
    po_id = fields.Many2one(related="wo_id.order_id", string="生产订单", store=True)
    product_id = fields.Many2one(related="wr_id.product_id", string="产品", store=True)
    process_id = fields.Many2one(related="wr_id.process_id", string="工序", store=True)
    employee_ids = fields.Many2many(related="wr_id.employee_ids", string="作业人员")
    uom_id = fields.Many2one("roke.uom", string="单位", related="product_id.uom_id", store=True)
    total = fields.Float(string="报废数量", digits='Production')
    note = fields.Text(string="备注")
    line_ids = fields.One2many("roke.scrap.order.line", "order_id", string="报废明细")

    def confirm(self):
        line_list = []
        scrap_id = self.env['roke.scrap.order'].create({
            'alarm_id': self.alarm_id.id,
            'code': self.code,
            'state': self.state,
            'wr_id': self.wr_id.id,
            'total': self.total,
            'note': self.note,
        })
        if not scrap_id:
            raise UserError('数据填写错误，数据创建失败!')
        for line in self.line_ids:
            line_list.append(
                (0, 0, {
                    "order_id": scrap_id.id,
                    "state": line.state,
                    "reason_id": line.reason_id.id,
                    "qty": line.qty,
                    "clean_qty": line.clean_qty,
                    "allow_qty": line.allow_qty,
                    "note": line.note
                })
            )
        scrap_id.write({'line_ids':line_list})


class RokeScrapOrderLineNew(models.TransientModel):
    _name = "roke.scrap.order.line.new"
    _description = "报废单明细"

    order_id = fields.Many2one("roke.scrap.order.wizard", string="报废单", ondelete='cascade')
    state = fields.Selection([
        ("待确认", "待确认"), ("确认", "确认"), ("部分处理", "部分处理"), ("处理完成", "处理完成"), ("取消", "取消")
    ], related="order_id.state", default="待确认", tracking=True, string="状态")
    wr_id = fields.Many2one("roke.work.record", related="order_id.wr_id", string="报工记录")
    wo_id = fields.Many2one(related="wr_id.work_order_id", string="工单", store=True)
    product_id = fields.Many2one(related="order_id.product_id", string="产品", store=True)
    uom_id = fields.Many2one("roke.uom", string="单位", related="product_id.uom_id", store=True)
    reason_id = fields.Many2one("roke.scrap.reason", string="报废原因", ondelete='restrict')
    qty = fields.Float(string="报废数量", digits='Production')
    clean_qty = fields.Float(string="已处理数量", digits='Production')
    allow_qty = fields.Float(string="可处理数量", compute="_compute_allow_qty", store=True, digits='Production')
    note = fields.Text(string="备注")