<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- 列表视图 -->
    <record id="view_roke_task_eqpt_check_item_tree" model="ir.ui.view">
        <field name="name">roke.task.eqpt.check.item.tree</field>
        <field name="model">roke.task.eqpt.check.item</field>
        <field name="arch" type="xml">
            <tree>
                <field name="task_id"/>
                <field name="equipment_id"/>
                <field name="check_time"/>
            </tree>
        </field>
    </record>

    <!-- 表单视图 -->
    <record id="view_roke_task_eqpt_check_item_form" model="ir.ui.view">
        <field name="name">roke.task.eqpt.check.item.form</field>
        <field name="model">roke.task.eqpt.check.item</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <field name="task_id"/>
                    <field name="equipment_id"/>
                    <field name="inspection_plan_id"/>
                    <field name="employee_id"/>
                    <field name="check_time"/>
                </group>
                <notebook>
                    <page string="检查项明细">
                        <field name="line_ids">
                            <tree editable="bottom">
                                <field name="check_item"/>
                                <field name="current_value"/>
                                <field name="reference_value"/>
                                <field name="check_result"/>
                            </tree>
                        </field>
                    </page>
                </notebook>
            </form>
        </field>
    </record>

    <!-- 动作和菜单 -->
    <record id="action_roke_task_eqpt_check_item" model="ir.actions.act_window">
        <field name="name">任务设备检查项</field>
        <field name="res_model">roke.task.eqpt.check.item</field>
        <field name="view_mode">tree,form</field>
    </record>

    <!--    <menuitem id="menu_roke_task_eqpt_check_item_root" name="任务设备检查项" sequence="10"/>-->
    <!--    <menuitem id="menu_roke_task_eqpt_check_item" -->
    <!--              name="任务设备检查项"-->
    <!--              parent="menu_roke_task_eqpt_check_item_root"-->
    <!--              action="action_roke_task_eqpt_check_item"/>-->
</odoo>