from odoo import models, fields, api

class InheritRokeScrapOrder(models.Model):
    _inherit = "roke.scrap.order"

    container_code = fields.Char(string='容器编号')

    def action_in_stock_move(self):
        wizard = self.env["roke.scrap.in.stock.wizard"].create(self._get_scrap_vals())
        return {
            'name': '报废入库',
            'type': 'ir.actions.act_window',
            'view_type': 'form',
            'view_mode': 'form',
            'target': 'new',
            'res_id': wizard.id,
            'res_model': 'roke.scrap.in.stock.wizard',
        }

    def _get_scrap_vals(self):
        """
        获取报废入库单明细
        :return:
        """
        line_ids = []
        for line in self.line_ids:
            line_ids.append((0, 0, {
                "scrap_line_id": line.id,
                "qty": line.qty,
            }))

        return {
            "line_ids": line_ids,
        }

class InheritRokeScrapOrderLine(models.Model):
    _inherit = "roke.scrap.order.line"

    employee_id = fields.Many2one('roke.employee', string="作业人员")
    date = fields.Date(string="业务日期")