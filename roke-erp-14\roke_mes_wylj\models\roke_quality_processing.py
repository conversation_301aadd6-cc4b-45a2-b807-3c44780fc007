from odoo import models, fields, api


class RokeQualityProcessing(models.Model):
    _name = 'roke.quality.processing'
    _description = '质检处理单'
    _rec_name = 'code'

    code = fields.Char('编号', required=True,
                       default=lambda self: self.env['ir.sequence'].next_by_code('roke.quality.processing.code'))
    processing_type = fields.Selection([
        ('分选单', '分选单'),
        ('返修单', '返修单'),
        ('报废单', '报废单')
    ], string='类型', required=True)
    task_id = fields.Many2one('roke.production.task', string='生产任务')
    picking_id = fields.Many2one('roke.mes.stock.picking', string='采购入库单')
    sale_picking_id = fields.Many2one('roke.mes.stock.picking', string='销售发货单')
    employee_id = fields.Many2one('roke.employee', string='作业人员')
    processing_date = fields.Date('业务日期', default=fields.Date.today)
    quantity = fields.Float('处理数量')
    quality_order_id = fields.Many2one('roke.quality.order', string='质检单')
    state = fields.Selection([
        ('草稿', '草稿'),
        ('确认', '确认'),
    ], string='状态', default='草稿')
    line_ids = fields.One2many('roke.quality.processing.line', 'processing_id', string='不合格明细')


    def action_confirm(self):
        for record in self:
            if record.processing_type in ['分选单', '返修单']:
                # 生成新的质检单逻辑
                quality_order_obj = self.env["roke.quality.order.wizard"]

                quality_order_vals = {
                    "processing_id": record.id,
                    "line_ids": [(0, 0, {
                        "processing_line_id": line.id,
                        "product_id": line.product_id.id,
                        "unqualified_qty": line.unqualified_qty,
                    }) for line in record.line_ids if line.unqualified_qty > 0]
                }
                res = quality_order_obj.create(quality_order_vals)
                return {
                    'type': 'ir.actions.act_window',
                    'name': '选择生成质检单',
                    'res_model': 'roke.quality.order.wizard',
                    'view_mode': 'form',
                    'target': 'new',
                    'res_id': res.id,
                }
            if record.processing_type == '报废单':
                # 生成报废单逻辑
                if self.line_ids:
                    details = []
                    for line in self.line_ids:
                        details.append((0, 0, {
                            # "reason_id": line.repair_reason.id,
                            "qty": line.unqualified_qty,
                            "note": line.note,
                            "product_id": line.product_id.id,
                        }))
                        self.env["roke.scrap.order"].create({
                            # "wo_id": self.work_id.id,
                            "product_id": line.product_id.id,
                            "total": line.unqualified_qty,
                            "line_ids": details
                        })

                # 生成报废入库单逻辑，其中如果是到货检要生成退库单
                picking_obj = self.env["roke.mes.stock.picking"]
                # picking_move_obj = self.env["roke.mes.stock.move"]
                if self.quality_order_id.quality_type == '到货检':
                    picking_type = self.env.ref('roke_mes_stock.stock_picking_type_purchase_in')
                    picking_line_vals = []
                    src_location = picking_type.src_location_id
                    dest_location = picking_type.dest_location_id
                    for line in self.line_ids:
                        picking_line_vals.append((0, 0, {
                            "product_id": line.product_id.id,
                            # "qty": line.unqualified_qty,
                        }))
                    picking_vals = {
                        "picking_type_id": picking_type.id,
                        "src_location_id": src_location.id,
                        "dest_location_id": dest_location.id,
                        "picking_date": fields.Date.today(),
                        "origin": self.picking_id.code,
                        "move_line_ids": picking_line_vals,
                        "is_red_order": True,
                    }
                    picking_obj.create(picking_vals)
                else:
                    # 生成报废入库单逻辑
                    picking_type = self.env.ref('roke_mes_wylj.wylj_stock_picking_type_order_in')
                    picking_line_vals = []
                    src_location = picking_type.src_location_id
                    dest_location = picking_type.dest_location_id
                    for line in self.line_ids:
                        picking_line_vals.append((0, 0, {
                            "product_id": line.product_id.id,
                            "qty": line.unqualified_qty,
                        }))
                    picking_vals = {
                        "picking_type_id": picking_type.id,
                        "src_location_id": src_location.id,
                        "dest_location_id": dest_location.id,
                        "picking_date": fields.Date.today(),
                        "origin": self.picking_id.code,
                        "move_line_ids": picking_line_vals,
                    }
                    picking_obj.create(picking_vals)
                record.state = '确认'
        return True

    def _get_picking_type(self):
        picking_type = self.env['roke.mes.stock.picking.type'].search([
            ('type', '=', '出库'), ('dest_location_id.location_type', '=', "客户位置")
        ])
        return picking_type[:1]


class RokeQualityProcessingLine(models.Model):
    _name = 'roke.quality.processing.line'
    _description = '质检处理单明细'

    processing_id = fields.Many2one('roke.quality.processing', string='质检处理单')
    product_id = fields.Many2one('roke.product', string='产品')
    unqualified_qty = fields.Float('不合格数量')
    note = fields.Char('不合格原因')
    task_id = fields.Many2one('roke.production.task', string='生产任务记录')
    picking_id = fields.Many2one('roke.mes.stock.picking', string='入库单需求记录')
