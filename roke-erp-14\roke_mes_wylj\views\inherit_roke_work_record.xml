<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="view_roke_wylj_inherit_production_record_form" model="ir.ui.view">
        <field name="name">roke.wylj.inherit.production.record.form</field>
        <field name="model">roke.work.record</field>
        <field name="inherit_id" ref="roke_mes_production.view_roke_work_record_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='product_state_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='salary_type']" position="attributes">F
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//label[@for='salary']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='team_salary_type']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='employee_ids']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='project_code']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='salary_total']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='origin_salary_reduce']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='punish_amount']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>


            <xpath expr="//field[@name='product_id']" position="after">
                <field name="mold_id" />
                <field name="tool_id" />
                <field name="equipment_id" />
            </xpath>
            <xpath expr="//field[@name='work_time']" position="after">
                <field name="workshop_id" />
                <field name="container_code" />
            </xpath>
        </field>
    </record>
</odoo>