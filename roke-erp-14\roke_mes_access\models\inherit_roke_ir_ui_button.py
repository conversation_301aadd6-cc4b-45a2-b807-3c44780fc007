#!/usr/bin/env python
# -*- coding:utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
from odoo.addons.roke_mes_access.models.inherit_res_groups import TOP_MENUS_LIST
from lxml import etree

class InheritMesRokeIrUiButton(models.Model):
    _inherit = 'roke.ir.ui.button'

    def get_submenu_list(self, parent_menu_id, result):
        """
        获取下级菜单
        """
        menu_obj = self.sudo().env['ir.ui.menu']
        menu_ids = menu_obj.search([('active', '=', True), ('parent_id', '=', parent_menu_id.id)])
        if menu_ids:
            for menu in menu_ids:
                result.append(menu.id)
                self.get_submenu_list(menu, result)
        else:
            result.append(parent_menu_id.id)
        return result

    def init(self):
        # 获取所有Mes菜单
        all_menu = []
        for menu in TOP_MENUS_LIST:
            menu_child = []
            menu_id = self.sudo().env.ref(menu, raise_if_not_found=False)
            if menu_id:
                all_menu.append(menu_id.id)
                all_menu = all_menu + self.get_submenu_list(menu_id, menu_child)
        # 遍历处理含动作的菜单
        view_obj = self.sudo().env['ir.ui.view']
        model_obj = self.sudo().env['ir.model']
        btn_obj = self.sudo().env['roke.ir.ui.button']
        menu_ids = self.sudo().env['ir.ui.menu'].browse(all_menu)
        for menu in menu_ids:
            if menu.action and 'ir.actions.act_window' in str(menu.action) and menu.action.res_model:
                # 查找菜单下模型视图
                model_id = model_obj.search([('model', '=', menu.action.res_model)], limit=1)
                views = view_obj.search([('model', '=', menu.action.res_model),
                                         ('mode', '=', 'primary'),
                                         ('type', '=', 'form'),
                                         ('active', '=', True)])
                # 遍历按钮
                for view in views:
                    xml_arch = etree.XML(view.arch_base)
                    xml_btns = xml_arch.xpath("//button")
                    xml_btn_list = []
                    for btn in xml_btns:
                        if btn.get("name") and btn.get("string", btn.get("smart_button")):
                            xml_btn_list.append(
                                {"sign": btn.get("name"), "name": btn.get("string", btn.get("smart_button"))})
                    # 按钮存储
                    for xml_btn in xml_btn_list:
                        button = btn_obj.search([
                            ('sign', '=', xml_btn.get("sign")), ('model', '=', model_id.model),
                            ('model_id', '=', model_id.id), ('view_id', '=', view.id)
                        ])
                        if not button:
                            btn_obj.create({
                                "type": "button", "sign": xml_btn.get("sign"), "name": xml_btn.get("name"),
                                "model": model_id.model, "model_id": model_id.id, "view_id": view.id,
                            })