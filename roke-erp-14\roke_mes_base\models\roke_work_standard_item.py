# -*- coding: utf-8 -*-
"""
Description:
    作业规范/标准
Versions:
    Created by www.inspur.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from urllib import parse


class RokeWorkStandardItem(models.Model):
    _name = "roke.work.standard.item"
    _description = "作业规范/标准"
    _order = "sequence"

    erp_id = fields.Char(string="ERP ID")
    sequence = fields.Integer(string="序号", default=10)
    title = fields.Char(string="标题")
    name = fields.Text(string="内容")
    process_id = fields.Many2one("roke.process", string="工序", ondelete="cascade")
    routing_line_id = fields.Many2one("roke.routing.line", string="工艺明细", ondelete="cascade")
    product_id = fields.Many2one("roke.product", string="产品", ondelete="cascade")
    work_center_id = fields.Many2one("roke.work.center", string="工作中心", ondelete="cascade")
    process_category_id = fields.Many2one("roke.process.category", string="工序类别", ondelete="cascade")
    image_1920 = fields.Image('图片说明')
    image_128 = fields.Image('图片说明', related="image_1920", max_width=128, max_height=128)
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)

    def get_image_preview_url(self, file_type=None):
        """
        获取图片预览地址
        :param file_type: 入参‘base64’或预览地址
        :return:
        """
        base_url = self.sudo().env['ir.config_parameter'].get_param('web.base.url')
        attachment = self.sudo().env['ir.attachment'].search([
            ("res_model", "=", "roke.work.standard.item"),
            ("res_id", "=", self.id),
            ("res_field", "=", "image_1920")
        ])  # 必须带sudo，原因见odoo-14.0/odoo/addons/base/models/ir_attachment.py 第441行
        if not attachment:
            return False
        if file_type == "base64":
            return [{
                "id": attachment.id,
                "name": attachment.name,
                "type": "base64",
                "data": attachment.datas,
                'is_picture': False
            }]
        if not attachment.access_token:
            attachment.generate_access_token()
        if attachment.mimetype == "application/pdf":
            # pdf 预览
            content_url = parse.quote("/web/content/%s?access_token=%s" % (str(attachment.id), attachment.sudo().access_token))
            url = "%s/web/static/lib/pdfjs/web/viewer.html?file=%s" % (base_url, content_url)
            is_picture = False
        else:
            # 图片 预览
            url = "%s/web/image/%s?access_token=%s" % (base_url, str(attachment.id), attachment.sudo().access_token)
            is_picture = True
        return [{
            "id": attachment.id,
            "name": attachment.name,
            "type": "url",
            "data": url,
            'is_picture': is_picture
        }]
