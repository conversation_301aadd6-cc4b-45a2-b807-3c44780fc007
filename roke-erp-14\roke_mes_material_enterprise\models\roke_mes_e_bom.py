# -*- coding: utf-8 -*-
"""
Description:
    设计BOM
Versions:
    Created by www.inspur.com<HuChuanwei>
"""
import math
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class RokeMESEBOM(models.Model):
    _name = "roke.mes.e_bom"
    _inherit = ['mail.thread']
    _description = "产品BOM"
    _rec_name = "code"

    code = fields.Char(string="编号", default="/", tracking=True, required=True, index=True, copy=False)
    active = fields.Boolean(string="有效的", default=True, tracking=True)
    product_id = fields.Many2one("roke.product", string="产品", tracking=True, required=True, index=True,
                                 ondelete="restrict")
    qty = fields.Float(string="数量", default=1, digits='KCSL')
    bom_line_ids = fields.One2many("roke.mes.e_bom.line", "bom_id", string="BOM清单")
    note = fields.Text(string="备注")
    production_qty = fields.Float(string="已投产", compute="_compute_production_qty", digits='SCSL')
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)
    uom_id = fields.Many2one("roke.uom", related="product_id.uom_id", string="计量单位")
    erp_id = fields.Char(string="ERP ID")

    @api.onchange('product_id')
    def _onchange_product_id(self):
        product_obj = self.env['roke.product'].search([('type', 'in', ['袋', '箱', '产成品', '半成品'])])
        product_list = [product.id for product in product_obj]
        return {"domain": {"product_id": [('id', 'in', product_list)]}}

    def _compute_production_qty(self):
        # 投产数
        po_line_id = self._context.get("po_line_id")
        for record in self:
            if po_line_id:
                tasks = self.env["roke.production.task"].search([
                    ("order_line_id", "=", po_line_id),
                    ("product_id", "=", record.product_id.id)
                ])
                record.production_qty = sum(tasks.mapped("plan_qty"))
            else:
                record.production_qty = 0

    def action_order_bom_production(self):
        """
        批量投产
        :return:
        """
        return self.bom_line_ids.action_order_bom_production()

    @api.model
    def create(self, vals):
        if vals.get("code") in ('/', None, False):
            vals["code"] = self.env['ir.sequence'].next_by_code('roke.mes.e_bom.code')
        res = super(RokeMESEBOM, self).create(vals)
        po_line_id = self._context.get("po_line_id")
        if po_line_id:
            self.env["roke.production.order.line"].browse(po_line_id).write({"e_bom_id": res.id})
        return res

    @api.constrains('product_id', 'bom_line_ids')
    def _check_bom_lines(self):
        for bom in self:
            for bom_line in bom.bom_line_ids:
                same_product = bom.product_id == bom_line.product_id
                if same_product:
                    raise ValidationError("BOM清单中 %s 不可以和表头产品相同." % bom.display_name)
            # bom.check_bom_recursion(bom)

    def check_bom_recursion(self, boms):
        """
        校验bom循环设置
        :return:
        """
        for child_bom in self.bom_line_ids.child_bom_id:
            if child_bom in boms:
                bom_show_str = '→'.join(boms.mapped("display_name")) + '→' + boms[0].display_name
                print(self.erp_id)
                raise ValidationError("BOM循环设置！！\n%s" % bom_show_str)
            boms += child_bom
            child_bom.check_bom_recursion(boms)

    def name_get(self):
        bom_invisible_product = self._context.get("bom_invisible_product")
        if bom_invisible_product:
            return [(bom.id, '%s' % bom.code) for bom in self]
        return [(bom.id, '%s%s' % (bom.code and '%s: ' % bom.code or '', bom.product_id.name)) for bom in self]

    def get_formview_id(self, access_uid=None):
        """JS获取form视图ID"""
        if self.env.context.get("bom_structure"):
            return self.env.ref('roke_mes_material_enterprise.view_roke_mes_e_bom_structure_edit_form').id
        else:
            return False

    def bom_import_entrance(self, vals):
        """
        TODO BOM导入
        :param vals: 入参格式：[{"code": "xx", "product": "xx", "product_qty": 1, "material": "xx", "material_qty": 1}, {···}]
        :return:
        """

        return

    def _get_copy_line_ids(self, line_id):
        return {
            "product_id": line_id.product_id.id,
            "routing_id": line_id.routing_id.id,
            "qty": line_id.qty,
            "loss_rate": line_id.loss_rate,
            "note": line_id.note,
            "child_bom_id": line_id.child_bom_id.id,
            "child_type": line_id.child_type,
        }

    def copy(self):
        res = super(RokeMESEBOM, self).copy()
        result = []
        for line_id in self.bom_line_ids:
            result.append((0, 0, self._get_copy_line_ids(line_id)))
        res.bom_line_ids = result
        return res


class RokeMESEBOMLine(models.Model):
    _name = "roke.mes.e_bom.line"
    _description = "产品BOM明细"

    bom_id = fields.Many2one("roke.mes.e_bom", string="BOM",  index=True, required=True, copy=False, ondelete="cascade")
    product_id = fields.Many2one("roke.product", string="物料", required=True, index=True, ondelete="restrict")
    routing_id = fields.Many2one("roke.routing", string="工艺路线", tracking=True, index=True)
    material_source = fields.Selection([("customer", "客户供料"), ("self", "自备/采购")], string="材料来源", default="self")
    qty = fields.Float(string="数量", default=1, digits='KCSL')
    loss_rate = fields.Float(string="损耗率(%)", default=0, digits='Production')
    note = fields.Text(string="备注")
    child_bom_id = fields.Many2one('roke.mes.e_bom', '下级BOM', store=True, index=True)
    production_qty = fields.Float(string="已投产", compute="_compute_production_qty", digits='SCSL')
    child_type = fields.Selection([("标准件", "标准件"), ("替代件", "替代件"), ("返回件", "返回件")], string="子项类型",
                                  default="标准件")
    child_bom_code = fields.Char(string="BOM编号", compute="_compute_child_bom_code", inverse='_inverse_set_child_bom')
    uom_id = fields.Many2one("roke.uom", related="product_id.uom_id", string="主计量")
    bom_material_attribute = fields.Selection([("主料", "主料"), ("辅料", "辅料"), ("其他", "其他")], string="BOM物料属性", default="主料")
    price = fields.Float(string="单价", digits='KCDJ')

    erp_id = fields.Char(string="ERP ID")

    _sql_constraints = [
        ('check_loss_rate', 'CHECK (loss_rate >= 0)', '损耗率不允许小于0！')
    ]

    @api.depends('child_bom_id')
    def _compute_child_bom_code(self):
        for line in self:
            line.child_bom_code = line.child_bom_id.code

    def _inverse_set_child_bom(self):
        BOMObj = self.env["roke.mes.e_bom"]
        for line in self:
            if line.child_bom_code:
                line.child_bom_id = BOMObj.search([("code", "=", line.child_bom_code)]).id
            else:
                line.child_bom_id = False

    def _compute_production_qty(self):
        # 已投产数量
        po_line_id = self._context.get("po_line_id")
        for record in self:
            if po_line_id:
                tasks = self.env["roke.production.task"].search([
                    ("order_line_id", "=", po_line_id),
                    ("product_id", "=", record.product_id.id)
                ])
                record.production_qty = sum(tasks.mapped("plan_qty"))
            else:
                record.production_qty = 0

    def action_show_order_bom_production(self):
        """
        查看生产任务
        :return:
        """
        po_line_id = self._context.get("po_line_id")
        return {
            'name': "生产订单所需原材料",
            'type': 'ir.actions.act_window',
            'res_model': 'roke.production.task',
            'view_mode': 'tree,form',
            'target': 'current',
            'domain': [("order_line_id", "=", po_line_id), ("product_id", "=", self.product_id.id)],
        }

    def _order_bom_get_pt_vals(self, product, qty, bom_line, e_bom_id, po_line=None):
        """
        BOM投产获取任务值
        :return:
        """
        if not po_line:
            po_line = self.env["roke.production.order.line"]
        SequenceObj = self.env['ir.sequence']
        return {
            "code": SequenceObj.next_by_code('roke.production.task.code'),
            "product_id": product.id,
            "plan_qty": qty,
            "routing_id": bom_line.routing_id.id or bom_line.product_id.routing_id.id,
            "e_bom_id": e_bom_id,
            "order_line_id": po_line.id,
            "customer_id": po_line.order_id.customer_id.id
        }

    def action_order_bom_production(self):
        """
        投产
        :return:
        """
        po_line_id = self._context.get("po_line_id")
        if not po_line_id:
            return
        rounding_type = self.env['ir.config_parameter'].sudo().get_param('e_bom.material.demand.rounding', default="精确计算")
        TaskObj = self.env["roke.production.task"]
        po_line = self.env["roke.production.order.line"].browse(po_line_id)
        task_vals = []
        for bom_line in self:
            product = bom_line.product_id
            qty = po_line.qty * bom_line.qty
            if qty <= 0:
                continue
            qty = qty * (1 + bom_line.loss_rate / 100)
            if rounding_type == "向下取整":
                qty = int(qty)
            elif rounding_type == "向上取整":
                qty = math.ceil(qty)
            e_bom_id = False
            if product.bom_ids:
                e_bom_id = product.bom_ids[0].id
            task_vals.append(self._order_bom_get_pt_vals(product, qty, bom_line, e_bom_id, po_line=po_line))
        if task_vals:
            tasks = TaskObj.create(task_vals)  # 创建生产任务
            tasks.create_work_order()  # 生成工单
            return {
                'name': "本次创建的需求任务",
                'type': 'ir.actions.act_window',
                'res_model': 'roke.production.task',
                'view_mode': 'tree,form',
                'target': 'current',
                'views': [
                    (self.env.ref('roke_mes_production.view_roke_production_task_tree').id, 'list'),
                    (self.env.ref('roke_mes_production.view_roke_production_task_form').id, 'form')
                ],
                'domain': [("id", "in", tasks.ids)],
            }
        raise ValidationError("没有要生产的物料")


    def name_get(self):
        return [(bom_line.id, '%s * %s → %s' % (bom_line.product_id.display_name, str(bom_line.qty), bom_line.bom_id.display_name)) for bom_line in self]

    @api.onchange('product_id')
    def _e_bom_onchange_product(self):
        for line in self:
            if not line.product_id:
                line.child_bom_id = False
                return {"value": {"child_bom_id": False, "routing_id": False, "bom_material_attribute": ""}}
            else:
                child_bom = self.env['roke.mes.e_bom'].search([("product_id", "=", line.product_id.id)], limit=1)
                default_routing = line.product_id.routing_id
                return {"value": {
                    "child_bom_id": child_bom,
                    "material_source": self.product_id.material_source,
                    "routing_id": default_routing.id,
                    "bom_material_attribute": line.product_id.bom_material_attribute
                }}

    @api.onchange("qty")
    def _onchange_qty(self):
        if self.qty <= 0:
            return {"warning": {
                "title": "数量填写错误", "message": "数量必须大于0"
            }, "value": {
                "qty": 1
            }}

    def get_formview_id(self, access_uid=None):
        """JS获取form视图ID"""
        if self.env.context.get("edit_bom_line"):
            return self.env.ref('roke_mes_material_enterprise.view_roke_edit_e_bom_line_wizard_form').id
        else:
            return False
