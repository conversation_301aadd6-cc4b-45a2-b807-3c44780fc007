import traceback  # -*- coding: utf-8 -*-
from odoo import models, fields, api, http, SUPERUSER_ID, _
from odoo.exceptions import UserError
import json, os, io
import logging
import base64
from datetime import datetime, timedelta
import math
from openpyxl import load_workbook
import requests

_logger = logging.getLogger(__name__)
headers = [('Content-Type', 'application/json; charset=utf-8')]


class SynchronizationsInterface(http.Controller):

    # 获取字段定义
    def _get_field_definitions(self,env,model_index):
        fields_info = {}
        field_obj = env['ir.model.fields']
        all_fields =  field_obj.search([('model_id.model', '=', model_index)])
        for field in all_fields:
            fields_info[field.name] = {
                'type': field.ttype,
                'model': field.model,
                "name":field.name,
                'relation': field.relation,
                'required': field.required,
                'readonly': field.readonly,

            }
        return fields_info
        # 工具方法：转换字段值

    def _convert_value(self, env, field_def, value):
        field_type = field_def['type']
        if not value:
            return False
        if field_type == 'many2one':
            related_model = field_def['relation']
            origin_model = field_def.get('model')
            if isinstance(value, str):  # 传入的是name
                # 查找是否已存在该名称的记录
                if origin_model == 'roke.mes.e_bom' or origin_model == 'roke.mes.e_bom.line' :
                    if field_def['name'] == 'product_id':
                        record = env[related_model].search([('code', '=', value)], limit=1)
                        if not record:
                          raise  UserError(f"(模型：{related_model})，未找到名称为 {value} 的记录,请检查是否产品已经同步")
                        return record.id
                    else:
                        record = env[related_model].search([('name', '=', value)], limit=1)
                        if not record:  # 不存在则创建
                            record = env[related_model].create({'name': value})
                        return record.id

                else:
                    record = env[related_model].search([('name', '=', value)], limit=1)
                    if not record:  # 不存在则创建
                        record = env[related_model].create({'name': value})
                    return record.id
            else:  # 传入的是id
                record = env[related_model].browse(value).exists()
                if not record:
                    raise UserError(f"关联模型 {related_model} 中不存在 ID 为 {value} 的记录")
                return record.id
        elif field_type == 'one2many':
            if not isinstance(value, list):
                raise UserError(f"字段 {field_def['name']} 应为列表类型")
            lines = []
            sub_field_def = self._get_field_definitions(env,field_def['relation'])
            for line in value:
                line_mode = line.get('mode', 'create')  # create edit delete
                line_erp_id = line.get('erp_id')
                line_data = {k: v for k, v in line.items() if k not in ('mode', )}
                processed_line = {}
                for key, val in line_data.items():
                    sub_field = sub_field_def.get(key)
                    if not sub_field:
                        raise  UserError( f"字段 {key} 不存在于模型 {field_def['relation']} 中，请检查输入字段是否正确。")

                    processed_line[key] = self._convert_value(env, sub_field, val)


                if line_mode == 'create':
                    record = env[field_def['relation']].search([('erp_id', '=', line_erp_id)], limit=1)
                    if  record:
                        raise UserError(f"行明细创建时，(模型：{field_def['relation']})中已找到,找到明细记录 erp_id:  {line_erp_id}")
                    lines.append((0, 0, processed_line))
                elif line_mode == 'edit':
                    if not line_erp_id:
                        raise UserError("明细行编辑明细时（mode 传的 为 edit）必须提供 erp_id")
                    record = env[field_def['relation']].search([('erp_id', '=', line_erp_id)],limit=1)
                    if not record:
                        raise UserError(f"(模型：{field_def['relation']})，未找到明细记录 erp_id:  {line_erp_id}")
                    lines.append((1, record.id, processed_line))
                elif line_mode == 'delete':
                    if not line_erp_id:
                        raise UserError("删除明细时必须提供 erp_id")
                    record = env[field_def['relation']].search([('erp_id', '=', line_erp_id)],limit=1)
                    if not record:
                        raise UserError(f"(模型：{field_def['relation']})，未找到明细记录 erp_id: {line_erp_id}")
                    lines.append((2, record.id))
            return lines
        elif field_type == 'many2many':
            if not isinstance(value, list):
                raise UserError(f"字段 {field_def['name']} 应为列表类型")
            records = env[field_def['relation']].browse(value).exists()
            if len(records) != len(value):
                missing_ids = set(value) - set(records.ids)
                raise UserError(f"(模型：{field_def['relation']})，以下 many2many ID 不存在：{missing_ids}")
            return [(6, 0, records.ids)]
        elif field_type == 'selection':
            selection_dict = dict(env[field_def['model']]._fields[field_def['name']].selection)
            # if value not in selection_values:
            #     raise UserError(f"字段 {field_def['name']} 的值不在选项[{selection_values}]中：{value}")
            # return value
            # 通过值（value）反向查找键（key）
            for key, val in selection_dict.items():
                if val == value:
                    return key
            raise UserError(f"字段 {field_def['name']} 的值不在选项[{selection_dict.values()}]中：{value}")
        elif field_type == 'boolean':
            return bool(value)
        elif field_type == 'float':
            return float(value)
        elif field_type == 'integer':
            return int(value)
        elif field_type in ['date', 'datetime']:
           if field_type == 'datetime':
               dt = datetime.strptime(value, "%Y-%m-%d %H:%M:%S")
               dt_utc = dt - timedelta(hours=8)
               return dt_utc.strftime("%Y-%m-%d %H:%M:%S")
        else:
            return value


    def _required_fields(self, model_index,header_data):
        errors = []
        required_fields = []
        if model_index == 'roke.product':
            required_fields = ['name']

        for field in required_fields:
            if field not in header_data:
                errors.append(field)
        if errors:
            raise UserError(f"缺少必填字段：{', '.join(errors)}")


    @http.route('/roke/wylj_generic/data_synchronization', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False,
                cors='*')
    def generic_data_synchronization(self):
        '''
         通用数据同步接口
        :return:
        '''
        model_index = http.request.jsonrequest.get('model_index', '')
        erp_id = http.request.jsonrequest.get('erp_id', '')
        mode = http.request.jsonrequest.get('mode', '')  # create edit delete
        header_data = http.request.jsonrequest.get('data', {}) or {}
        try:
            # 校验基础参数
            if not model_index:
                raise UserError('模型标识为必传项，请参考模型标识对应表！')
            if mode not in ['create', 'edit', 'delete']:
                raise UserError('非可执行操作类型!（创建-create,修改-edit,删除-delete）')
            if not erp_id:
                raise UserError('(第三方系统唯一标识字段erp_id缺失！')

            env = http.request.env(user=SUPERUSER_ID)
            model_obj = env[model_index]
            field_obj = env['ir.model.fields']
            model_id = env['ir.model'].search([('model', '=', model_index)], limit=1)

            if not model_id:
                raise UserError(f'模型标识【{model_index}】有误，请参考模型标识对应表！')



            field_definitions = self._get_field_definitions(env, model_index)

            # 预处理 header_data
            processed_header = {}
            for key, value in header_data.items():
                field_def = field_definitions.get(key)
                if not field_def:
                    raise UserError(f"字段 {key} 在模型 {model_index} 中不存在")
                processed_header[key] = processed_header[key] = self._convert_value(env, field_def, value)
            processed_header['erp_id'] = erp_id

            # 查询现有数据
            record = model_obj.search([('erp_id', '=', erp_id)], limit=1)

            # 执行操作
            if mode == 'create':
                if record:
                    raise UserError(f'erp_id【{erp_id}】在【{model_id.name}】表中已存在对应数据！')
                new_record = model_obj.create(processed_header)
                result = {'state': 'success', 'msgs': '创建成功', 'id': new_record.id}
            elif mode == 'edit':
                if not record:
                    raise UserError(f'erp_id【{erp_id}】在【{model_id.name}】表中未找到对应数据！')
                record.write(processed_header)
                result = {'state': 'success', 'msgs': '更新成功','id': record.id}
            elif mode == 'delete':
                if record:
                    record.unlink()
                    result = {'state': 'success', 'msgs': '删除成功', 'id': record.id}
                else:
                    raise UserError(f'erp_id【{erp_id}】在【{model_id.name}】表中未找到对应数据！')


        except Exception as e:
            result = {
                'state': 'error',
                'msgs': str(e),

            }
            http.request.env.cr.rollback()

        return result


