<odoo noupdate="1">
    <record id="stage_01" model="roke.abnormal.alarm.state">
        <field name="name">新的</field>
    </record>
    <record id="stage_02" model="roke.abnormal.alarm.state">
        <field name="name">已确认</field>
    </record>
    <record id="stage_03" model="roke.abnormal.alarm.state">
        <field name="name">处理中</field>
    </record>
    <record id="stage_04" model="roke.abnormal.alarm.state">
        <field name="name">已解决</field>
    </record>

    <record id="type_01" model="roke.abnormal.alarm.type">
        <field name="name">缺料断料</field>
    </record>
    <record id="type_02" model="roke.abnormal.alarm.type">
        <field name="name">模具异常</field>
    </record>
    <record id="type_03" model="roke.abnormal.alarm.type">
        <field name="name">工艺异常</field>
    </record>
    <record id="type_04" model="roke.abnormal.alarm.type">
        <field name="name">生产异常</field>
    </record>
    <record id="type_05" model="roke.abnormal.alarm.type">
        <field name="name">设备异常</field>
    </record>
    <record id="type_06" model="roke.abnormal.alarm.type">
        <field name="name">质量异常</field>
    </record>
</odoo>
