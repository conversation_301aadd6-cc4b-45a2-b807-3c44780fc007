<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--search-->
    <record id="view_roke_mold_ledger_search" model="ir.ui.view">
        <field name="name">roke.mold.ledger.search</field>
        <field name="model">roke.mold.ledger</field>
        <field name="arch" type="xml">
            <search string="模具台账">
                <field name="name"/>
                <field name="code"/>
                <field name="specification"/>
                <field name="use_customer"/>
                <field name="supplier_factory"/>
                <field name="mold_out_code"/>
                <field name="state"/>
                <field name="use_mold_frequency"/>
                <field name="mold_current_state"/>
                <field name="material"/>
                <field name="molding_time"/>
                <field name="service_intervals"/>
                <field name="responsible_person"/>
                <field name="argument_sheet"/>
                <field name="purchase_contracts"/>
                <field name="instructions"/>
                <field name="acceptance"/>
                <field name="recipient"/>
                <field name="mold_size"/>
                <field name="spare_circumstance"/>
                <field name="temperature_box_location"/>
                <field name="mandrels_number"/>
                <field name="collars_number"/>
                <field name="cavity_number"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_mold_ledger_tree" model="ir.ui.view">
        <field name="name">roke.mold.ledger.tree</field>
        <field name="model">roke.mold.ledger</field>
        <field name="arch" type="xml">
            <tree string="模具台账">
                <field name="name" optional="show"/>
                <field name="category_id" optional="show"/>
                <field name="inspection_plan_id" optional="show"/>
                <field name="state" optional="show"/>
                <field name="code" optional="show"/>
                <field name="mold_location_id" optional="show"/>
                <field name="specification" optional="show"/>
                <field name="supplier_factory" optional="show"/>
                <field name="arrival_time" optional="show"/>
                <field name="mold_maintain_id" optional="show"/>
                <field name="service_time" optional="show"/>
                <field name="service_intervals" optional="show"/>
            </tree>
        </field>
    </record>

    <record id="view_roke_mold_maintain_records_readonly_tree" model="ir.ui.view">
        <field name="name">roke.mold.maintain.records.readonly.tree</field>
        <field name="model">roke.mold.maintain.records</field>
        <field name="arch" type="xml">
            <tree string="保养记录">
                <field name="code" optional="show"/>
                <field name="maintain_user" widget="many2many_tags" optional="show"/>
                <field name="maintain_time" optional="show" width="200px"/>
                <field name="is_maintain" optional="show"/>
                <field name="maintain_type" optional="show"/>
                <field name="use_time" optional="show"/>
                <field name="state" optional="show"/>
                <field name="maintain_note" optional="show"/>
                <button name="start_action" type="object" string="开始保养"
                        class='oe_highlight oe_read_only' attrs="{'invisible': [('state', 'in', ('保养中', '完成', '转维修'))]}"/>
                <button name="confirm" type="object"
                        string="保养完成" class='oe_highlight oe_read_only' attrs="{'invisible': [('state', 'in', ('草稿', '完成', '转维修'))]}"/>
            </tree>
        </field>
    </record>
    <record id="view_roke_mold_repair_records_readonly_tree" model="ir.ui.view">
        <field name="name">roke.mold.repair.records.readonly.tree</field>
        <field name="model">roke.mold.repair.records</field>
        <field name="arch" type="xml">
            <tree string="维修记录">
                <field name="code" optional="show"/>
                <field name="submit_user" optional="show"/>
                <field name="repair_users" optional="show" widget="many2many_tags"/>
                <field name="start_repair_time" optional="show" width="200px"/>
                <field name="finish_repair_time" optional="show" width="200px"/>
                <field name="use_time" optional="show"/>
                <field name="state" optional="show"/>
                <field name="note" optional="show"/>
                <field name="result" optional="show"/>
                <field name="repair_state" optional="show"/>
                <button name="start_action" type="object" string="开始维修"
                        class='oe_highlight oe_read_only' attrs="{'invisible': [('state', 'in', ('维修中', '完成'))]}"/>
                <button name="confirm_repair" type="object" string="维修完成"
                        class='oe_highlight oe_read_only' attrs="{'invisible': [('state', 'in', ('草稿', '完成'))]}"/>
            </tree>
        </field>
    </record>
    <record id="view_roke_mold_pick_records_readonly_tree" model="ir.ui.view">
        <field name="name">roke.mold.pick.records.readonly.tree</field>
        <field name="model">roke.mold.pick.records</field>
        <field name="arch" type="xml">
            <tree string="数采记录">
                <field name="mold_id" optional="show"/>
                <field name="pick_time" optional="show"/>
                <field name="start_pick_data" optional="show"/>
                <field name="stop_pick_data" optional="show"/>
                <field name="pick_data" optional="show"/>
                <field name="note" optional="show"/>
            </tree>
        </field>
    </record>
    <record id="view_roke_mold_use_records_readonly_tree" model="ir.ui.view">
        <field name="name">roke.mold.use.records.readonly.tree</field>
        <field name="model">roke.mold.use.records</field>
        <field name="arch" type="xml">
            <tree string="使用记录">
                <field name="code" optional="show"/>
                <field name="type" optional="show"/>
                <field name="workshop_id" optional="show"/>
                <field name="equipment_id" optional="show"/>
<!--                <field name="acceptance_user" optional="show"/>-->
                <field name="acceptance_user_ids" optional="show" widget="many2many_tags"/>
                <field name="mold_location_id" optional="show"/>
                <field name="acceptance_time" optional="show"/>
                <field name="return_time" optional="show"/>
                <field name="use_note" optional="show"/>
                <field name="note" optional="show"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_mold_ledger_form" model="ir.ui.view">
        <field name="name">roke.mold.ledger.form</field>
        <field name="model">roke.mold.ledger</field>
        <field name="arch" type="xml">
            <form string="模具台账">
                <header>
                    <!-- <button name="mold_warranty" type="object"
                        string="报修" class='oe_highlight oe_read_only' attrs="{'invisible': [('state', '=', '维修')]}"/> -->
                    <field name="state" widget="statusbar" statusbar_visible="闲置,在产,维修,报废"/>
                </header>
<!--                <sheet>-->
                    <group col="4">
                        <group>
                            <field name="name"/>
                            <field name="code"/>
                            <field name="specification"/>
                            <field name="arrival_time" required="1"/>
                            <field name="work_team_id"/>
                            <field name="is_common_use"/>
                        </group>
                        <group>
                            <field name="service_intervals"/>
                            <field name="service_time"/>
                            <field name="equipment_id"/>
                            <field name="mold_location_id"/>
                            <field name="current_location_id"/>
                        </group>
                        <group>
                            <field name="use_customer"/>
                            <field name="department_id"/>
                            <field name="supplier_factory"/>
                            <field name="mold_maintain_id"/>
                            <field name="roke_company_id" string="当前基地" context="{'tree_view_ref': 'roke_mes_wylj.view_readonly_roke_company_tree'}"/>
<!--                            <field name="kq_location_id"/>-->
                        </group>
                        <group>
                            <field name="category_id"/>
                            <field name="inspection_plan_id"/>
                            <field name="part_number"/>
                            <field name="asset_number"/>
                           <field name="maintain_user_ids" widget="many2many_tags"/>
                        </group>
                    </group>
                    <group>
<!--                        <group>-->
<!--                            <field name="qr_code"/>-->
<!--                        </group>-->
                        <group>
                            <field name="create_date" invisible="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="保养记录">
                            <field name="maintain_records" readonly="1"
                                   context="{'tree_view_ref': 'roke_mes_wylj.view_roke_mold_maintain_records_readonly_tree'}"/>
                        </page>
                        <page string="维修记录">
                            <field name="repair_records" readonly="1"
                                   context="{'tree_view_ref': 'roke_mes_wylj.view_roke_mold_repair_records_readonly_tree'}"/>
                        </page>
                        <page string="点检记录">
                            <field name="inspection_record_ids" readonly="1"
                                   context="{'tree_view_ref': 'roke_mes_wylj.view_mold_inspection_record_tree'}"/>
                        </page>
                        <page string="使用记录">
                            <field name="use_records" readonly="1"
                                   context="{'tree_view_ref': 'roke_mes_wylj.view_roke_mold_use_records_readonly_tree'}"/>
                        </page>
                    </notebook>
                    <group id="g2">
                        <field name="note" placeholder="此处可以填写备注或描述"/>
                    </group>
<!--                </sheet>-->
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_mold_ledger_action" model="ir.actions.act_window">
        <field name="name">模具台账</field>
        <field name="res_model">roke.mold.ledger</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
    </record>
    <!--action-->
    <record id="view_readonly_roke_mold_ledger_action" model="ir.actions.act_window">
        <field name="name">模具台账</field>
        <field name="res_model">roke.mold.ledger</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">
            {'create': False, 'edit': False, 'delete': False}
        </field>
    </record>
    <!--menu-->
    <!--模具管理-->
    <menuitem id="roke_mold_management_menu_one" name="模具管理"
              web_icon="roke_mes_wylj,static/template/mo.png"
              sequence="510"/>
    <menuitem id="roke_return_receive_menu" name="归还领用"
              parent="roke_mes_wylj.roke_mold_management_menu_one"
              sequence="50"/>
    <menuitem id="roke_maintenance_menu" name="维修保养"
              parent="roke_mes_wylj.roke_mold_management_menu_one"
              sequence="100"/>
<!--    <menuitem id="roke_requisition_menu" name="申请单"-->
<!--              parent="roke_mes_wylj.roke_mold_management_menu_one"-->
<!--              sequence="110"/>-->
    <menuitem id="roke_mold_ledger_menu" name="模具台账" sequence="100"
              action="view_roke_mold_ledger_action"
              parent="roke_mes_wylj.roke_return_receive_menu"/>
    <menuitem id="readonly_roke_mold_ledger_menu" name="模具台账" sequence="110"
              action="view_readonly_roke_mold_ledger_action"
              parent="roke_mes_wylj.roke_return_receive_menu"/>

<!--    模具仓库-->
    <!--search-->
    <record id="view_roke_mold_location_search" model="ir.ui.view">
        <field name="name">roke.mold.location.search</field>
        <field name="model">roke.mold.location</field>
        <field name="arch" type="xml">
            <search string="模具仓库">
                <field name="name"/>
                <field name="code"/>
                <field name="note"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_mold_location_tree" model="ir.ui.view">
        <field name="name">roke.mold.location.tree</field>
        <field name="model">roke.mold.location</field>
        <field name="arch" type="xml">
            <tree string="模具仓库">
                <field name="name" optional="show"/>
                <field name="code" optional="show"/>
                <field name="note" optional="show"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_mold_location_form" model="ir.ui.view">
        <field name="name">roke.mold.location.form</field>
        <field name="model">roke.mold.location</field>
        <field name="arch" type="xml">
            <form string="模具仓库">
<!--                <sheet>-->
                    <group col="2">
                        <group>
                            <field name="code"/>
                            <field name="type"/>
                        </group>
                        <group>
                            <field name="name"/>
                        </group>
                    </group>
                    <group id="g2">
                        <field name="note" placeholder="此处可以填写备注或描述"/>
                    </group>
<!--                </sheet>-->
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_mold_location_action" model="ir.actions.act_window">
        <field name="name">模具仓库</field>
        <field name="res_model">roke.mold.location</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
    </record>
    <!--menu-->
<!--    <menuitem id="roke_mold_location_menu" name="模具仓库" sequence="200"-->
<!--              action="view_roke_mold_location_action"-->
<!--              parent="roke_mes_wylj.roke_return_receive_menu"/>-->

<!--    模具保养方案-->
    <!--search-->
    <record id="view_roke_mold_maintain_search" model="ir.ui.view">
        <field name="name">roke.mold.maintain.search</field>
        <field name="model">roke.mold.maintain</field>
        <field name="arch" type="xml">
            <search string="模具保养方案">
                <field name="code"/>
                <field name="name"/>
                <field name="note"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_mold_maintain_tree" model="ir.ui.view">
        <field name="name">roke.mold.maintain.tree</field>
        <field name="model">roke.mold.maintain</field>
        <field name="arch" type="xml">
            <tree string="模具保养方案">
                <field name="code" optional="show"/>
                <field name="name" optional="show"/>
                <field name="mold_ids" widget="many2many_tags" optional="show"/>
                <field name="frequency" optional="show"/>
                <field name="frequency_unit" optional="show"/>
                <field name="next_maintenance_date" optional="show"/>
                <field name="note" optional="show"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_mold_maintain_form" model="ir.ui.view">
        <field name="name">roke.mold.maintain.form</field>
        <field name="model">roke.mold.maintain</field>
        <field name="arch" type="xml">
            <form string="模具保养方案">
<!--                <sheet>-->
                    <div name="button_box" class="oe_button_box">
                        <button type="object"
                                name="action_view_cron"
                                class="oe_stat_button"
                                icon="fa-truck" attrs="{'invisible':[('cron_count','=',0)]}">
                            <field name="cron_count" widget="statinfo" string="定时任务" help="查看定时任务"/>
                            <field name="ir_cron" invisible="1"/>
                        </button>
                    </div>
                    <group col="4">
                        <group>
                            <field name="name" required="1"/>
                            <label for="frequency"/>
                            <div class="o_row">
                                <field name="frequency" required="1"/>
                                <span>
                                    <field name="frequency_unit" required="1"/>
                                </span>
                            </div>
                        </group>
                        <group>
                            <field name="mold_ids" required="1" widget="many2many_tags" options="{'no_create': True}"/>
                            <field name="security_measures"/>
                        </group>
                        <group>
                            <field name="last_maintenance_date" readonly="1" force_save="1"/>
                        </group>
                        <group>
                            <field name="next_maintenance_date"/>
                        </group>
                    </group>
                    <group id="g2">
                        <field name="note" placeholder="此处可以填写备注或描述"/>
                    </group>
                    <notebook>
                        <page string="保养项目">
                             <field name="item_ids"/>
                        </page>
                         <page string="保养记录">
                             <field name="maintain_records" readonly="1">
                                 <tree>
                                     <field name="maintain_id" invisible="1"/>
                                     <field name="mold_id"/>
                                     <field name="maintain_user" widget="many2many_tags"/>
                                     <field name="maintain_time"/>
                                     <field name="is_maintain"/>
                                     <field name="state"/>
                                     <field name="note"/>
                                     <button name="start_action" type="object" string="开始保养"
                                             class='oe_highlight oe_read_only' attrs="{'invisible': [('state', 'in', ('保养中', '完成'))]}"/>
                                     <button name="confirm" type="object" string="保养完成"
                                             class='oe_highlight oe_read_only' attrs="{'invisible': [('state', 'in', ('草稿', '完成'))]}"/>
                                 </tree>
                             </field>
                         </page>
                    </notebook>
<!--                </sheet>-->
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_mold_maintain_action" model="ir.actions.act_window">
        <field name="name">模具保养方案</field>
        <field name="res_model">roke.mold.maintain</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
    </record>
    <!--menu-->
    <menuitem id="roke_mold_maintain_menu" name="模具保养方案" sequence="300"
              action="view_roke_mold_maintain_action"
              parent="roke_mes_wylj.roke_maintenance_menu"/>

<!--    保养任务-->
    <!--search-->
    <record id="view_roke_mold_maintain_records_search" model="ir.ui.view">
        <field name="name">roke.mold.maintain.records.search</field>
        <field name="model">roke.mold.maintain.records</field>
        <field name="arch" type="xml">
            <search string="保养任务">
                <field name="code"/>
                <field name="mold_id"/>
                <field name="maintain_id"/>
                <field name="maintain_user"/>
                <field name="state"/>
                <field name="maintain_note"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_mold_maintain_records_tree" model="ir.ui.view">
        <field name="name">roke.mold.maintain.records.tree</field>
        <field name="model">roke.mold.maintain.records</field>
        <field name="arch" type="xml">
            <tree string="保养任务" default_order="code desc">
                <field name="code" optional="show"/>
                <field name="mold_id" optional="show"/>
                <field name="maintain_id" optional="show"/>
                <field name="maintain_user" widget="many2many_tags" optional="show"/>
                <field name="maintain_time" optional="show"/>
                <field name="is_maintain" optional="show"/>
                <field name="maintain_type" optional="show"/>
                <field name="use_time" optional="show"/>
                <field name="state" optional="show"/>
                <field name="maintain_note" optional="show"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_mold_maintain_records_form" model="ir.ui.view">
        <field name="name">roke.mold.maintain.records.form</field>
        <field name="model">roke.mold.maintain.records</field>
        <field name="arch" type="xml">
            <form string="保养任务">
                <header>
                    <button name="start_action" type="object" string="开始保养"
                        class='oe_highlight oe_read_only' attrs="{'invisible': [('state', 'in', ('保养中', '完成', '转维修'))]}"/>
                    <button name="confirm" type="object"
                        string="保养完成" class='oe_highlight oe_read_only' attrs="{'invisible': [('state', 'in', ('草稿', '完成', '转维修'))]}"/>
<!--                    <button name="create_repair_orders" type="object" string="生成维修单"-->
<!--                            class="oe_highlight" attrs="{'invisible': [('state', '=', ('转维修'))]}"/>-->
                    <field name="state" widget="statusbar"/>
                </header>
<!--                <sheet>-->
                    <group col="2">
                        <group>
                            <field name="mold_id"/>
                            <field name="maintain_user" widget="many2many_tags"/>
                            <field name="workshop_id"/>
                            <field name="use_time"/>
                            <field name="is_maintain"/>
                            <field name="maintain_note"/>
                        </group>
                        <group>
                            <field name="maintain_id"/>
                            <field name="maintain_apply_user"/>
                            <field name="maintain_time"/>
                            <field name="maintain_type" required="1"/>
                            <field name="result"/>
                            <field name="picture" widget="image"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="保养项目">
                            <field name="item_ids">
                                <tree editable="bottom">
                                    <field name="order_id" invisible="1"/>
                                    <field name="item_id" required="1"/>
                                    <field name="execute_user_id" readonly="1"/>
                                    <field name="execute_time" readonly="1"/>
                                    <field name="description" readonly="1"/>
                                    <field name="state" readonly="1"/>
                                    <button name="execute_entrance" type="object" icon="fa-check" string="完成" attrs="{'invisible':[('state', '!=', 'wait')]}" context="{'state': 'finish'}"/>
                                    <button name="execute_entrance" type="object" icon="fa-eye-slash" string="忽略" attrs="{'invisible':[('state', '!=', 'wait')]}" context="{'state': 'ignore'}"/>
                                    <button name="execute_entrance" type="object" icon="fa-reply-all" string="置为等待" attrs="{'invisible':[('state', '!=', 'ignore')]}" context="{'state': 'wait'}"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                    <group id="g2">
                        <field name="note" placeholder="此处可以填写备注或描述"/>
                    </group>
<!--                </sheet>-->
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_mold_maintain_records_action" model="ir.actions.act_window">
        <field name="name">保养任务</field>
        <field name="res_model">roke.mold.maintain.records</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="view_ids" eval="[(5,0,0),
              (0,0,{'view_mode': 'tree','view_id': ref('roke_mes_wylj.view_roke_mold_maintain_records_tree')}),
              (0,0,{'view_mode': 'form','view_id': ref('roke_mes_wylj.view_roke_mold_maintain_records_form')})]"/>
    </record>
    <!--menu-->
    <menuitem id="roke_mold_maintain_records_menu" name="保养任务" sequence="400"
              action="view_roke_mold_maintain_records_action"
              parent="roke_mes_wylj.roke_maintenance_menu"/>

<!--    维修记录-->
    <!--search-->
    <record id="view_roke_mold_repair_records_search" model="ir.ui.view">
        <field name="name">roke.mold.repair.records.search</field>
        <field name="model">roke.mold.repair.records</field>
        <field name="arch" type="xml">
            <search string="维修任务">
                <field name="code"/>
                <field name="mold_id"/>
                <field name="submit_user"/>
                <field name="repair_users"/>
                <field name="state"/>
                <field name="note"/>
                <field name="result"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_mold_repair_records_tree" model="ir.ui.view">
        <field name="name">roke.mold.repair.records.tree</field>
        <field name="model">roke.mold.repair.records</field>
        <field name="arch" type="xml">
            <tree string="维修任务">
                <field name="code" optional="show"/>
                <field name="mold_id" optional="show"/>
                <field name="submit_user" optional="show"/>
                <field name="repair_users" optional="show" widget="many2many_tags"/>
                <field name="start_repair_time" optional="show"/>
                <field name="finish_repair_time" optional="show"/>
                <field name="use_time" optional="show"/>
                <field name="repair_state" optional="show"/>
                <field name="state" optional="show"/>
                <field name="note" optional="show"/>
                <field name="result" optional="show"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_mold_repair_records_form" model="ir.ui.view">
        <field name="name">roke.mold.repair.records.form</field>
        <field name="model">roke.mold.repair.records</field>
        <field name="arch" type="xml">
            <form string="维修任务">
                <header>
                    <button name="start_action" type="object" string="开始维修"
                        class='oe_highlight oe_read_only' attrs="{'invisible': [('state', 'in', ('维修中', '完成', '委外'))]}"/>
                    <button name="confirm_repair" type="object" string="维修完成"
                        class='oe_highlight oe_read_only' attrs="{'invisible': [('state', 'in', ('草稿', '完成', '委外'))]}"/>
                    <button name="outsourcing_end" type="object" class='oe_highlight' string="结束委外" access="1"
                            attrs="{'invisible':[('state', '!=', '委外')]}"/>
                    <button name="action_outsourcing" type="object" class='oe_highlight' string="委外" access="1"
                        attrs="{'invisible':[('state', 'not in', ['维修中', '草稿'])]}"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <field name="repair_state" invisible="1"/>
<!--                <sheet>-->
                    <widget name="web_ribbon" text="车间模具维修单" bg_color="bg-success" attrs="{'invisible': [('repair_state', '=', '模具中心维修单')]}"/>
                    <widget name="web_ribbon" text="模具中心维修单" bg_color="bg-warning" attrs="{'invisible': [('repair_state', '=', '车间模具维修单')]}"/>
                    <group col="3">
                        <group>
                            <field name="mold_id"/>
                            <field name="failure_id"/>
                            <field name="repair_origin"/>
                            <field name="finish_repair_time"/>
                        </group>
                        <group>
                            <field name="mold_code"/>
                            <field name="repair_failure_id"/>
                            <field name="submit_user"/>
                            <field name="repair_users" widget="many2many_tags"/>
                        </group>
                        <group>
                            <field name="m_location"/>
                            <field name="cavities_number"/>
                            <field name="start_repair_time"/>
                            <field name="use_time"/>
                        </group>
                    </group>
                    <group id="g2">
                        <field name="note" placeholder="此处可以填写故障原因"/>
                    </group>
                    <group id="g3">
                        <field name="result" placeholder="此处可以填写处理结果"/>
                    </group>
                    <group id="g4">
                        <group>
                            <field name="fault_picture" widget="image" img_width="128" img_height="128" height="128"/>
                        </group>
                        <group>
                            <field name="repair_picture" widget="image" img_width="128" img_height="128" height="128"/>
                        </group>
                    </group>
<!--                </sheet>-->
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_mold_repair_records_action" model="ir.actions.act_window">
        <field name="name">维修任务</field>
        <field name="res_model">roke.mold.repair.records</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="view_ids" eval="[(5,0,0),
              (0,0,{'view_mode': 'tree','view_id': ref('roke_mes_wylj.view_roke_mold_repair_records_tree')}),
              (0,0,{'view_mode': 'form','view_id': ref('roke_mes_wylj.view_roke_mold_repair_records_form')})]"/>
    </record>
    <!--menu-->
    <menuitem id="roke_mold_repair_records_menu" name="维修任务" sequence="500"
              action="view_roke_mold_repair_records_action"
              parent="roke_mes_wylj.roke_return_receive_menu"/>

<!--    模具领用-->
    <!--search-->
    <record id="view_roke_mold_use_records_search" model="ir.ui.view">
        <field name="name">roke.mold.use.records.search</field>
        <field name="model">roke.mold.use.records</field>
        <field name="arch" type="xml">
            <search string="模具领用">
                <field name="code"/>
                <field name="line_id"/>
                <field name="equipment_id"/>
<!--                <field name="acceptance_user"/>-->
                <field name="acceptance_user_ids"/>
                <field name="use_note"/>
                <field name="note"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_mold_use_records_tree" model="ir.ui.view">
        <field name="name">roke.mold.use.records.tree</field>
        <field name="model">roke.mold.use.records</field>
        <field name="arch" type="xml">
            <tree string="模具领用">
                <field name="code" optional="show"/>
                <field name="line_id" optional="show"/>
                <field name="equipment_id" optional="show"/>
<!--                <field name="acceptance_user" optional="show"/>-->
                <field name="acceptance_user_ids" optional="show" widget="many2many_tags"/>
                <field name="acceptance_time" optional="show"/>
<!--                <field name="return_time" optional="show"/>-->
                <field name="use_note" optional="show"/>
                <field name="note" optional="show"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_mold_use_records_form" model="ir.ui.view">
        <field name="name">roke.mold.use.records.form</field>
        <field name="model">roke.mold.use.records</field>
        <field name="arch" type="xml">
            <form string="模具领用">
<!--                <sheet>-->
                    <group col="2">
                        <group>
                            <field name="type" invisible="1"/>
                            <field name="line_id" domain="[('state', '=', '闲置')]"/>
<!--                            <field name="acceptance_user" required="1"/>-->
                            <field name="acceptance_user_ids" required="1" widget="many2many_tags"/>
                            <field name="acceptance_time"/>
                        </group>
                        <group>
                            <field name="equipment_id" required="1" domain="[('mold_id', '=', False)]"/>
                            <field name="use_note"/>
                        </group>
                    </group>
                    <group id="g2">
                        <field name="note" placeholder="此处可以填写备注说明"/>
                    </group>
<!--                </sheet>-->
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_mold_use_records_action" model="ir.actions.act_window">
        <field name="name">模具领用</field>
        <field name="res_model">roke.mold.use.records</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[('type', '=', '领用')]</field>
        <field name="context">
            {'default_type': '领用'}
        </field>
        <field name="view_ids" eval="[(5,0,0),
              (0,0,{'view_mode': 'tree','view_id': ref('roke_mes_wylj.view_roke_mold_use_records_tree')}),
              (0,0,{'view_mode': 'form','view_id': ref('roke_mes_wylj.view_roke_mold_use_records_form')})]"/>
    </record>
    <!--menu-->
    <menuitem id="roke_mold_use_records_menu" name="模具领用" sequence="600"
              action="view_roke_mold_use_records_action"
              parent="roke_mes_wylj.roke_return_receive_menu"/>

<!--    模具归还-->
    <!--search-->
    <record id="view_roke_mold_use_records_return_search" model="ir.ui.view">
        <field name="name">roke.mold.use.records.return.search</field>
        <field name="model">roke.mold.use.records</field>
        <field name="arch" type="xml">
            <search string="模具归还">
                <field name="code"/>
                <field name="line_id"/>
                <field name="equipment_id" string="解绑设备"/>
<!--                <field name="acceptance_user" string="归还人"/>-->
                <field name="acceptance_user_ids" string="归还人"/>
                <field name="note" string="归还备注"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_mold_use_records_return_tree" model="ir.ui.view">
        <field name="name">roke.mold.use.records.return.tree</field>
        <field name="model">roke.mold.use.records</field>
        <field name="arch" type="xml">
            <tree string="模具归还">
                <field name="code" optional="show" string="模具归还编号"/>
                <field name="line_id" optional="show"/>
                <field name="equipment_id" optional="show" string="解绑设备"/>
<!--                <field name="acceptance_user" optional="show" string="归还人"/>-->
                <field name="acceptance_user_ids" optional="show" string="归还人" widget="many2many_tags"/>
                <field name="return_time" optional="show" string="归还时间"/>
                <field name="note" optional="show" string="归还备注"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_mold_use_records_return_form" model="ir.ui.view">
        <field name="name">roke.mold.use.records.return.form</field>
        <field name="model">roke.mold.use.records</field>
        <field name="arch" type="xml">
            <form string="模具归还">
<!--                <sheet>-->
                    <group col="2">
                        <group>
                            <field name="type" invisible="1"/>
                            <field name="line_id" domain="[('state', '=', '在产')]"/>
<!--                            <field name="acceptance_user" required="1" string="归还人"/>-->
                            <field name="acceptance_user_ids" required="1" string="归还人" widget="many2many_tags"/>
                            <field name="use_id" invisible="1"/>
                        </group>
                        <group>
                            <field name="equipment_id" readonly="1" force_save="1" string="解绑设备"/>
                            <field name="return_time"/>
                        </group>
                    </group>
                    <group id="g2">
                        <field name="note" string="归还备注" placeholder="此处可以填写备注说明"/>
                    </group>
<!--                </sheet>-->
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_mold_use_records_return_action" model="ir.actions.act_window">
        <field name="name">模具归还</field>
        <field name="res_model">roke.mold.use.records</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[('type', '=', '归还')]</field>
        <field name="context">
            {'default_type': '归还', 'edit': False}
        </field>
        <field name="view_ids" eval="[(5,0,0),
              (0,0,{'view_mode': 'tree','view_id': ref('roke_mes_wylj.view_roke_mold_use_records_return_tree')}),
              (0,0,{'view_mode': 'form','view_id': ref('roke_mes_wylj.view_roke_mold_use_records_return_form')})]"/>
    </record>
    <!--menu-->
    <menuitem id="roke_mold_use_records_return_menu" name="模具归还" sequence="700"
              action="view_roke_mold_use_records_return_action"
              parent="roke_mes_wylj.roke_return_receive_menu"/>

    <!--设备绑定模具-->
    <record id="szjzh_inherit_view_roke_mes_equipment_form" model="ir.ui.view">
        <field name="name">szjzh.inherit.roke.mes.equipment.form</field>
        <field name="model">roke.mes.equipment</field>
        <field name="inherit_id" ref="roke_mes_equipment.view_roke_mes_equipment_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='code']" position="after">
                <field name="mold_id" readonly="1"/>
            </xpath>
            <xpath expr="//field[@name='work_center_id']" position="after">
<!--                <field name="erp_equipment_id"/>-->
            </xpath>
        </field>
    </record>

<!--    报废申请单-->
    <!--search-->
    <record id="view_roke_mold_scrapping_request_search" model="ir.ui.view">
        <field name="name">roke.mold.scrapping.request.search</field>
        <field name="model">roke.mold.scrapping.request</field>
        <field name="arch" type="xml">
            <search string="报废申请单">
                <field name="code"/>
                <field name="mold_id"/>
                <field name="apply_user"/>
                <field name="problem_description"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_mold_scrapping_request_tree" model="ir.ui.view">
        <field name="name">roke.mold.scrapping.request.tree</field>
        <field name="model">roke.mold.scrapping.request</field>
        <field name="arch" type="xml">
            <tree string="报废申请单">
                <field name="code" optional="show"/>
                <field name="mold_id" optional="show"/>
                <field name="specification" optional="show"/>
                <field name="mold_out_code" optional="show"/>
                <field name="cavities_number" optional="show"/>
                <field name="apply_user" optional="show"/>
                <field name="apply_date" optional="show"/>
                <field name="problem_description" optional="show"/>
                <field name="result" optional="show"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_mold_scrapping_request_form" model="ir.ui.view">
        <field name="name">roke.mold.scrapping.request.form</field>
        <field name="model">roke.mold.scrapping.request</field>
        <field name="arch" type="xml">
            <form string="报废申请单">
                <header>
                    <button name="action_approve" type="object" string="提交" invisible="1"/>
                    <button name="action_turn_down" type="object" string="驳回" invisible="1"/>
                    <field name="state" widget="statusbar"/>
                </header>
<!--                <sheet>-->
                    <group col="2">
                        <group>
                            <field name="mold_id"/>
                            <field name="mold_out_code"/>
                            <field name="category_id"/>
                            <field name="apply_user"/>
                        </group>
                        <group>
                            <field name="specification"/>
                            <field name="cavities_number"/>
                            <field name="use_department"/>
                            <field name="apply_date"/>
                        </group>
                    </group>
                    <group id="g2">
                        <field name="problem_description" placeholder="此处可以填写问题描述"/>
                        <field name="result"/>
                        <field name="product_header"/>
                        <field name="use_factory"/>
                        <field name="sale_company"/>
                        <field name="production_department"/>
                        <field name="technology_center"/>
                        <field name="finance_department"/>
                        <field name="manager_approval"/>
                    </group>
<!--                </sheet>-->
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_mold_scrapping_request_action" model="ir.actions.act_window">
        <field name="name">报废申请单</field>
        <field name="res_model">roke.mold.scrapping.request</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="view_ids" eval="[(5,0,0),
              (0,0,{'view_mode': 'tree','view_id': ref('roke_mes_wylj.view_roke_mold_scrapping_request_tree')}),
              (0,0,{'view_mode': 'form','view_id': ref('roke_mes_wylj.view_roke_mold_scrapping_request_form')})]"/>
    </record>
    <!--menu-->
<!--    <menuitem id="roke_mold_scrapping_request_menu" name="报废申请单" sequence="800"-->
<!--              action="view_roke_mold_scrapping_request_action"-->
<!--              parent="roke_mes_wylj.roke_requisition_menu"/>-->

<!--    封存申请单-->
    <!--search-->
    <record id="view_roke_mold_sealed_request_search" model="ir.ui.view">
        <field name="name">roke.mold.sealed.request.search</field>
        <field name="model">roke.mold.sealed.request</field>
        <field name="arch" type="xml">
            <search string="封存申请单">
                <field name="code"/>
                <field name="mold_id"/>
                <field name="apply_user"/>
                <field name="problem_description"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_mold_sealed_request_tree" model="ir.ui.view">
        <field name="name">roke.mold.sealed.request.tree</field>
        <field name="model">roke.mold.sealed.request</field>
        <field name="arch" type="xml">
            <tree string="封存申请单">
                <field name="code" optional="show"/>
                <field name="mold_id" optional="show"/>
                <field name="specification" optional="show"/>
                <field name="mold_out_code" optional="show"/>
                <field name="cavities_number" optional="show"/>
                <field name="apply_user" optional="show"/>
                <field name="apply_date" optional="show"/>
                <field name="problem_description" optional="show"/>
                <field name="result" optional="show"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_mold_sealed_request_form" model="ir.ui.view">
        <field name="name">roke.mold.sealed.request.form</field>
        <field name="model">roke.mold.sealed.request</field>
        <field name="arch" type="xml">
            <form string="封存申请单">
                <header>
                    <button name="action_approve" type="object" string="提交" invisible="1"/>
                    <button name="action_turn_down" type="object" string="驳回" invisible="1"/>
                    <field name="state" widget="statusbar"/>
                </header>
<!--                <sheet>-->
                    <group col="2">
                        <group>
                            <field name="mold_id"/>
                            <field name="mold_out_code"/>
                            <field name="category_id"/>
                            <field name="apply_user"/>
                        </group>
                        <group>
                            <field name="specification"/>
                            <field name="cavities_number"/>
                            <field name="use_department"/>
                            <field name="apply_date"/>
                        </group>
                    </group>
                    <group id="g2">
                        <field name="problem_description" placeholder="此处可以填写问题描述"/>
                        <field name="result"/>
                        <field name="product_header"/>
                        <field name="use_factory"/>
                        <field name="sale_company"/>
                        <field name="production_department"/>
                        <field name="technology_center"/>
                        <field name="finance_department"/>
                        <field name="manager_approval"/>
                    </group>
<!--                </sheet>-->
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_mold_sealed_request_action" model="ir.actions.act_window">
        <field name="name">封存申请单</field>
        <field name="res_model">roke.mold.sealed.request</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="view_ids" eval="[(5,0,0),
              (0,0,{'view_mode': 'tree','view_id': ref('roke_mes_wylj.view_roke_mold_sealed_request_tree')}),
              (0,0,{'view_mode': 'form','view_id': ref('roke_mes_wylj.view_roke_mold_sealed_request_form')})]"/>
    </record>
    <!--menu-->
<!--    <menuitem id="roke_mold_sealed_request_menu" name="封存申请单" sequence="900"-->
<!--              action="view_roke_mold_sealed_request_action"-->
<!--              parent="roke_mes_wylj.roke_requisition_menu"/>-->

    <!--保养项目-->
    <!--search-->
    <record id="view_roke_mold_maintain_item_search" model="ir.ui.view">
        <field name="name">roke.mold.maintain.item.search</field>
        <field name="model">roke.mold.maintain.item</field>
        <field name="arch" type="xml">
            <search string="保养项目">
                <field name="code"/>
                <field name="name"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_mold_maintain_item_tree" model="ir.ui.view">
        <field name="name">roke.mold.maintain.item.tree</field>
        <field name="model">roke.mold.maintain.item</field>
        <field name="arch" type="xml">
            <tree string="保养项目">
                <field name="code"/>
                <field name="name"/>
                <field name="note"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_mold_maintain_item_form" model="ir.ui.view">
        <field name="name">roke.mold.maintain.item.form</field>
        <field name="model">roke.mold.maintain.item</field>
        <field name="arch" type="xml">
            <form string="保养项目">
<!--                <sheet>-->
                <group col="4">
                    <group>
                        <field name="code" required="1"/>
                        <field name="secure_text"/>
                    </group>
                    <group>
                        <field name="name" required="1"/>
                    </group>
                    <group>
                        <field name="company_id" groups="base.group_multi_company" options="{'no_open': True, 'no_create': True}"/>
                    </group>
                    <group></group>
                </group>
                <group>
                    <field name="note" placeholder="此处录入设备描述或内部备注"/>
                </group>

<!--                </sheet>-->
            </form>
        </field>
    </record>
    <!--action-->
    <record id="action_roke_mold_maintain_item" model="ir.actions.act_window">
        <field name="name">保养项目</field>
        <field name="res_model">roke.mold.maintain.item</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
    </record>

    <menuitem id="menu_roke_mold_maintain_item" name="保养项目" sequence="600"
              action="action_roke_mold_maintain_item"
              parent="roke_mes_wylj.roke_maintenance_menu"/>
</odoo>
