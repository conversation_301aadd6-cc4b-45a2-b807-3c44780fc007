<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="views_inherit_wylj_roke_product_form" model="ir.ui.view">
        <field name="name">inherit_wylj_roke_product_form</field>
        <field name="model">roke.product</field>
        <field name="inherit_id" ref="roke_mes_base.view_roke_product_form"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='quality_1']" position="replace">
                <group name="quality_1">
                    <group>
                        <field name="atrol_scheme_id" domain="[('state', '=', '生效中'), ('type', '=', '巡检')]"
                               options="{'no_create': True}"/>
                    </group>
                    <group>
                        <field name="arrival_scheme_id" domain="[('state', '=', '生效中'), ('type', '=', '到货检')]"
                               options="{'no_create': True}"/>
                    </group>
                    <group>
                        <field name="shipment_scheme_id" domain="[('state', '=', '生效中'), ('type', '=', '发货检')]"
                               options="{'no_create': True}"/>
                    </group>
                </group>
            </xpath>
            <xpath expr="//notebook" position="inside">
                <page string="数采标准">
<!--                    <gruop>-->
<!--                        <field name="excel_file"/>-->
<!--                    </gruop>-->
                    <button name="action_import_standard" type="object" string="导入标准" class='btn-primary'/>
                    <field name="collection_standard_ids">
                        <tree editable="bottom">
                            <field name="equipment_id" required="1"/>
                            <field name="check_item_id" domain="[('equipment_id', '=', equipment_id)]" required="1"/>
                            <field name="standard_value"/>
                            <field name="lower_limit"/>
                            <field name="upper_limit"/>
                            <field name="active"/>
                        </tree>
                    </field>
                </page>
            </xpath>
        </field>
    </record>
</odoo>