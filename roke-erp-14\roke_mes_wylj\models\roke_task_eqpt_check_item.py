from odoo import api, fields, models
from datetime import datetime, timedelta


class RokeTaskEqptCheckItem(models.Model):
    _name = "roke.task.eqpt.check.item"
    _description = "任务设备检查项"

    # 新增字段
    task_id = fields.Many2one("roke.production.task", string="任务")
    equipment_id = fields.Many2one("roke.mes.equipment", string="设备")
    inspection_plan_id = fields.Many2one("roke.data.collection.inspection.plan", string="点检方案")
    employee_id = fields.Many2one("roke.employee", string="作业人员")
    check_time = fields.Datetime(string="点检时间", default=fields.Datetime.now())
    line_ids = fields.One2many("roke.task.eqpt.check.item.line", "item_id", string="检查项明细")

class RokeTaskEqptCheckItemLine(models.Model):
    _name = "roke.task.eqpt.check.item.line"
    _description = "任务设备检查项明细"

    item_id = fields.Many2one("roke.task.eqpt.check.item", string="检查项")
    check_item = fields.Many2one("roke.mes.equipment.item", string="点检项")
    current_value = fields.Char(string="当前值")
    reference_value = fields.Char(string="参考值")
    check_result = fields.Selection(
        [('合格', '合格'), ('不合格', '不合格')], string="点检结果")


