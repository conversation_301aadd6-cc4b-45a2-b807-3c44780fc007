<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="inherit_roke_pub_notice_rule_views" model="ir.ui.view">
        <field name="name">inherit_roke_pub_notice_rule_views_from</field>
        <field name="model">roke.pub.notice.rule</field>
        <field name="inherit_id" ref="roke_pub_notice.roke_pub_notice_rule_form"/>
        <field name="arch" type="xml">
            <xpath expr="//page" position="replace">
                <page string="消息定义">
                    <field name="line_ids">
                        <tree>
                            <field name="domain"/>
                            <field name="send_type"/>
                            <field name="user_field_ids" widget="many2many_tags"/>
                            <field name="user_ids" widget="many2many_tags"/>
                            <field name="group_ids" widget="many2many_tags"/>
                            <field name="template_id"/>
                            <field name="page_url"/>
                            <field name="mail_model_name"/>
                            <field name="param_ids" widget="many2many_tags" options="{'make_display_name': true}"/>
                            <button string="配置参数" name="add_message_parameters" type="object" class="btn-primary"/>
                        </tree>
                    </field>
                </page>
            </xpath>
        </field>
    </record>

    <record id="inherit_roke_pub_notice_rule_msgs_form" model="ir.ui.view">
        <field name="name">inherit.roke.pub.notice.rule.msgs.form</field>
        <field name="model">roke.pub.notice.rule.msgs</field>
        <field name="inherit_id" ref="roke_pub_notice.roke_pub_notice_rule_msgs_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='page_url']" position="after">
                <field name="mail_model_name"/>
            </xpath>
        </field>
    </record>
</odoo>