from odoo import api, fields, models
import json, base64, qrcode, io
from odoo.osv import expression
import requests
from odoo.exceptions import ValidationError

def make_qr(string):
    qr = qrcode.QRCode(
        version=4,  # 生成二维码尺寸的大小 1-40 1:21*21（21+(n-1)*4）
        error_correction=qrcode.constants.ERROR_CORRECT_M,  # L:7% M:15% Q:25% H:30%
        box_size=8,  # 每个格子的像素大小
        border=2,  # 边框的格子宽度大小
    )
    qr.add_data(string)
    qr.make(fit=True)
    img = qr.make_image()
    buf = io.BytesIO()
    img.save(buf)
    img_stream = buf.getvalue()
    return base64.b64encode(img_stream)

class InheritRokeMesStockLocation(models.Model):
    _inherit = 'roke.mes.stock.location'

    is_hardware = fields.Boolean(string="可硬件调拨", default=False)
    roke_company_id = fields.Many2one('roke.company', string='公司')

    container_code = fields.Char(string='容器编号')
    is_container = fields.Boolean(string='是否容器')

    @api.onchange("parent_id")
    def onchange_parent_id(self):
        for record in self:
            record.roke_company_id = record.parent_id.roke_company_id.id

class RokeMesEquipmentItem(models.Model):
    _name = 'roke.mes.equipment.item'
    _description = '数采检查项'
    _rec_name = 'item_name'

    item_id = fields.Char(string='检查项id')
    item_name = fields.Char(string='检查项名称')
    equipment_id = fields.Many2one("roke.mes.equipment", string='设备')



class InheritRokeMesEquipment(models.Model):
    _inherit = 'roke.mes.equipment'

    maintenance_scheme_id = fields.Many2one("roke.mes.maintenance.scheme", string="保养方案", tracking=True)
    maintenance_frequency = fields.Integer(string="保养频次", tracking=True)
    maintenance_interval = fields.Integer(string="保养周期", tracking=True)
    asset_number = fields.Char(string="资产编号", tracking=True)
    work_team_id = fields.Many2one("roke.work.team", string="所属班组", tracking=True)
    workshop_id = fields.Many2one("roke.department", string="所属车间", tracking=True)
    qr_code = fields.Char(string="二维码内容", tracking=True)
    spot_plan_ids = fields.Many2one("roke.mes.eqpt.spot.check.plan", string="点检方案")
    check_record_ids = fields.One2many("roke.mes.eqpt.spot.check.record", "equipment_id", string="点检记录")

    _sql_constraints = [('wylj_equipment_code_unique', 'unique(code)',
                         '内部编号不可重复!')]

    roke_company_id = fields.Many2one('roke.company', string='当前公司')
    current_location_id = fields.Many2one("roke.mes.stock.location", string="当前仓库",
                                          domain="[('is_hardware', '=', True)]", tracking=True)

    ck_location_id = fields.Many2one("roke.mes.stock.location", string="当前仓库", domain="[('type', '=', '仓库')]", tracking=True)
    kq_location_id = fields.Many2one("roke.equipment.location", string="库区", domain="[('type', '=', '库区')]", tracking=True)
    kw_location_id = fields.Many2one("roke.equipment.location", string="库位", domain="[('type', '=', '库位')]", tracking=True)
    maintain_user_ids = fields.Many2many("res.users", string="保养人")
    apikey = fields.Char(string="apikey")
    item_line_ids = fields.One2many("roke.mes.equipment.item", "equipment_id", string="检查项")
    item_line_id = fields.Many2one("roke.mes.equipment.item", string="开合模调试")

    @api.onchange("item_line_ids")
    def _onchange_item_line_ids(self):
        if self.item_line_ids:
            return {"domain": {"item_line_id": [("id", "=", self.self.item_line_ids.ids)]}}

    def data_acquisition_pull(self):
        base_url = self.env['ir.config_parameter'].get_param('web.base.url')
        if '************' in base_url:
            res = requests.post(url="http://************:83/go/api/getlabel", params={'Apikey': self.apikey},
                                timeout=10)
            response_data = res.json()
            # 从响应数据中获取return部分
            return_data = response_data.get("return", {}).get("label", {}).get("list", [])
            if return_data:
                self.item_line_ids.unlink()
                for record in return_data:
                    self.env['roke.mes.equipment.item'].create({
                        'item_id': record.get("labelID"),
                        'item_name': record.get("dataname"),
                        'equipment_id': self.id
                    })
        else:
            raise ValidationError("非内网环境，无法获取！")

    @api.model
    def create(self, vals):
        if not vals.get("qr_code", ''):
            vals.update({'qr_code': vals["code"]})
        return super(InheritRokeMesEquipment, self).create(vals)

    @api.model
    def _name_search(self, name, args=None, operator='ilike', limit=100, name_get_uid=None):
        args = args or []
        if operator == 'ilike' and not (name or '').strip():
            domain = []
        else:
            domain = ['|', ('name', operator, name), ('code', operator, name)]
        return self._search(expression.AND([domain, args]), limit=limit, access_rights_uid=name_get_uid)

    @api.model
    def create_maintain_order(self):
        for item in self:
            if item.maintenance_interval and len(item.maintenance_ids) % item.maintenance_interval == 0:
                # 生成保养单
                self.env['roke.mes.maintenance.order'].create({
                    'equipment_id': item.id,
                    'maintenance_scheme_id': item.maintenance_scheme_id.id,
                    'type': 'maintain',
                    'user_id': [(6, 0, item.maintain_user_ids.ids)]
                })

    def btn_cr_qr_image(self):
        for record in self:
            # 生成
            record.qr_image = make_qr(record.qr_code)

    def action_scrap_equipment(self):
        """
        设备报废功能
        将设备状态设置为报废
        """
        for record in self:
            # 检查设备当前状态
            if record.e_state == '报废':
                raise ValidationError(f"设备【{record.name}】已经是报废状态，无需重复操作！")

            # 检查设备是否在维修中
            if record.in_repair:
                raise ValidationError(f"设备【{record.name}】正在维修中，无法报废！请先完成维修。")

            # 检查是否有未完成的维修/保养单
            unfinished_orders = self.env['roke.mes.maintenance.order'].search([
                ('equipment_id', '=', record.id),
                ('state', 'not in', ['finish', 'cancel'])
            ])
            if unfinished_orders:
                raise ValidationError(f"设备【{record.name}】存在未完成的维修/保养单，无法报废！请先处理完相关单据。")

            # 更新设备状态为报废
            record.write({
                'e_state': '报废',
                'active': False  # 设置为非活跃状态
            })

            # 记录操作日志
            record.message_post(
                body=f"设备已报废，操作人：{self.env.user.name}，操作时间：{fields.Datetime.now()}",
                message_type='notification'
            )

    def action_cancel_scrap_equipment(self):
        """
        取消设备报废
        将设备状态从报废恢复为闲置
        """
        for record in self:
            # 检查设备当前状态
            if record.e_state != '报废':
                raise ValidationError(f"设备【{record.name}】不是报废状态，无法取消报废！")

            # 恢复设备状态
            record.write({
                'e_state': '闲置',
                'active': True  # 恢复为活跃状态
            })

            # 记录操作日志
            record.message_post(
                body=f"设备报废已取消，恢复为闲置状态，操作人：{self.env.user.name}，操作时间：{fields.Datetime.now()}",
                message_type='notification'
            )


class EqptInspectionPlanLine(models.Model):
    _name = "eqpt.inspection.plan.line"
    _description = "设备点检方案明细"
    _rec_name = "item_id"

    line_id = fields.Many2one("roke.mes.eqpt.spot.check.plan", string="点检方案", required=True, ondelete='cascade')
    item_id = fields.Many2one("roke.mes.eqpt.check.item", string="点检项目", required=True)
    method = fields.Char(string="方法")
    standard = fields.Char(string="标准")

class InheritRokeMesEquipmentSpotCheckPlan(models.Model):
    _inherit = "roke.mes.eqpt.spot.check.plan"

    item_ids = fields.One2many("eqpt.inspection.plan.line", 'line_id', string="点检项目")

class InheritRokeMesEquipmentSpotLine(models.Model):
    _inherit = "roke.mes.eqpt.spot.check.line"

    @api.depends('check_item_id')
    def _compute_standard_value(self):
        for record in self:
            if record.check_item_id and record.record_id.check_plan_id:
                inspection_plan_line = self.env['eqpt.inspection.plan.line'].search(
                    [("line_id", "=", record.record_id.check_plan_id.id), ("item_id", "=", record.check_item_id.id)])
                if inspection_plan_line.standard:
                    record.standard_value = inspection_plan_line.standard
                else:
                    record.standard_value = ''
            else:
                record.standard_value = ''