from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from odoo.exceptions import UserError
import requests
import json

def _get_pd(env, index="SCSL"):
    return env["decimal.precision"].precision_get(index)

class InheritRokeWorkOrder(models.Model):
    _inherit = "roke.work.order"

    mold_id = fields.Many2one(related="task_id.mold_id", string='模具', store=True)
    tool_id = fields.Many2one(related="task_id.tool_id", string='工装', store=True)
    equipment_id = fields.Many2one(related="task_id.equipment_id", string="设备", store=True)
    priority = fields.Selection(related="task_id.priority", string='优先级', store=True)
    workshop_id = fields.Many2one(related="task_id.workshop_id", string='指派车间', store=True)
    container_record_ids = fields.One2many("roke.container.record", "work_order_id", string='更换容器记录')

    # 新增字段
    employee_id = fields.Many2one('roke.employee', string='报工人')
    start_time = fields.Datetime(string='开工时间')
    total_working_hours = fields.Float(string='总加工时(小时)')
    processing_duration = fields.Float(string='加工时长(小时)')
    unqualified_qty = fields.Float(string="不合格数")

    state = fields.Selection(selection_add=[("调试中", "调试中"), ("调试完成", "调试完成"), ("自检完成", "自检完成")], ondelete={"调试中": "set default", "调试完成": "set default", "自检完成": "set default"})
    work_debug_record_ids = fields.One2many("roke.work.debug.record", "work_order_id", string="调试记录")

    def finish_report_work_order(self, qty, unqualified_qty, work_hours, finish_time=fields.Datetime.now(), wr_id=False, move=True):
        """
        工单完成
        :return:
        """
        # 修改当前工单完成数
        finish_qty = round(self.finish_qty + qty, _get_pd(self.env))
        unqualified_qty = round(self.unqualified_qty + unqualified_qty, _get_pd(self.env))
        new_work_hours = round(self.work_hours + work_hours, 2)
        write_dict = {
            "finish_qty": finish_qty,
            "unqualified_qty": unqualified_qty,
            "work_hours": new_work_hours,
            "finish_time": finish_time,
            "state": "自检完成"
        }
        if not self.wo_manual_start and not self.wo_start_time:
            # 不是手工开工，且无报工记录时，这里写入开工时间
            write_dict["wo_start_time"] = fields.Datetime.now()
        check_qty = self._get_finish_check_qty(finish_qty, unqualified_qty)
        if check_qty >= self.plan_qty:
            # 根据工单是否手工完工处理工单的状态和完工时间
            if self.wo_manual_finish:
                write_dict.pop("state")
            else:
                write_dict["state"] = "已完工"
                write_dict["wo_finish_time"] = fields.Datetime.now()
            # write_dict["state"] = "已完工"
            # if not self.wo_manual_finish:
            #     write_dict["wo_finish_time"] = fields.Datetime.now()
        self.write(write_dict)
        if self.task_id:
            self.task_id.finish_report_task(qty)
        if self.check_create_production_result(wr_id):
            self.env["roke.production.result"].create(self._get_production_result_vals(qty, wr_id))
        # 回写主工单工时 TODO 子工单
        if self.wo_child_type == "child" and self.main_wo_id:
            main_write_dict = {
                "work_hours": self.main_wo_id.work_hours + work_hours
            }
            # 所有子工序都完工，主工单完工
            if self.main_wo_id.child_wo_ids.filtered(lambda child: child.state == "未完工"):
                main_state = "未完工"
            else:
                main_state = self.state
            main_write_dict["state"] = main_state
            if self.main_wo_id.child_wo_ids[-1] == self:
                main_write_dict["finish_time"] = finish_time
                main_write_dict["finish_qty"] = self.main_wo_id.finish_qty + qty
            self.main_wo_id.write(main_write_dict)

    def update_state_paused(self):
        """更新状态 -- 暂停"""
        self.write({'state': '暂停'})
        return True

    def update_state_unfinished(self):
        """更新状态 -- 自检完成"""
        self.write({'state': '自检完成'})
        return True

    @api.depends('start_time')
    def _compute_processing_duration(self):
        for record in self:
            if record.start_time:
                record.processing_duration = (fields.Datetime.now() - record.start_time).total_seconds() / 3600
            else:
                record.processing_duration = 0.0

    # def call_controller_method(self):
    #     """从模型方法调用控制器内部API"""
    #     self.ensure_one()
    #
    #     # 准备请求数据
    #     request_data = {
    #         'apikey': self.id,
    #     }
    #
    #     try:
    #         # 通过request环境调用控制器
    #         response = self.env['ir.http']._request_endpoint(
    #             http.route('/api/internal/work_order', type='json', auth='user'),
    #             'POST',
    #             {'args': request_data}
    #         )
    #
    #         if response.get('status') == 'success':
    #             # 处理成功响应
    #             return True
    #         else:
    #             raise UserError(_("控制器处理失败"))
    #
    #     except Exception as e:
    #         raise UserError(_("调用控制器出错: %s") % str(e))

    def create_work_record(self):
        if self.state != '自检完成':
            raise ValidationError(f"工单状态为{self.state}，禁止报工")
        base_url = self.env['ir.config_parameter'].get_param('web.base.url')
        result = requests.post(
            f"{base_url}/roke/wylj/create_work_record_schedule",
            data=json.dumps({"apikey": self.equipment_id.apikey, "product_id": self.product_id.id}),
            headers={"Content-Type": "application/json"}
        )
        if result.status_code == 200:
            result = json.loads(result.content).get("result")
            if result.get("state") == "error":
                raise UserError(result.get("msgs"))



