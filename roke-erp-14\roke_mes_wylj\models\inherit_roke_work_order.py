from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from odoo.exceptions import UserError
import requests
import json

class InheritRokeWorkOrder(models.Model):
    _inherit = "roke.work.order"

    mold_id = fields.Many2one(related="task_id.mold_id", string='模具', store=True)
    tool_id = fields.Many2one(related="task_id.tool_id", string='工装', store=True)
    equipment_id = fields.Many2one(related="task_id.equipment_id", string="设备", store=True)
    priority = fields.Selection(related="task_id.priority", string='优先级', store=True)
    workshop_id = fields.Many2one(related="task_id.workshop_id", string='指派车间', store=True)
    container_record_ids = fields.One2many("roke.container.record", "work_order_id", string='更换容器记录')

    # 新增字段
    employee_id = fields.Many2one('roke.employee', string='报工人')
    start_time = fields.Datetime(string='开工时间')
    total_working_hours = fields.Float(string='总加工时(小时)')
    processing_duration = fields.Float(string='加工时长(小时)')
    unqualified_qty = fields.Float(string="不合格数")

    state = fields.Selection(selection_add=[("调试中", "调试中"), ("调试完成", "调试完成"), ("自检完成", "自检完成")], ondelete={"调试中": "set default", "调试完成": "set default", "自检完成": "set default"})

    def update_state_paused(self):
        """更新状态 -- 暂停"""
        self.write({'state': '暂停'})
        return True

    def update_state_unfinished(self):
        """更新状态 -- 自检完成"""
        self.write({'state': '自检完成'})
        return True


    @api.depends('start_time')
    def _compute_processing_duration(self):
        for record in self:
            if record.start_time:
                record.processing_duration = (fields.Datetime.now() - record.start_time).total_seconds() / 3600
            else:
                record.processing_duration = 0.0

    # def call_controller_method(self):
    #     """从模型方法调用控制器内部API"""
    #     self.ensure_one()
    #
    #     # 准备请求数据
    #     request_data = {
    #         'apikey': self.id,
    #     }
    #
    #     try:
    #         # 通过request环境调用控制器
    #         response = self.env['ir.http']._request_endpoint(
    #             http.route('/api/internal/work_order', type='json', auth='user'),
    #             'POST',
    #             {'args': request_data}
    #         )
    #
    #         if response.get('status') == 'success':
    #             # 处理成功响应
    #             return True
    #         else:
    #             raise UserError(_("控制器处理失败"))
    #
    #     except Exception as e:
    #         raise UserError(_("调用控制器出错: %s") % str(e))

    def create_work_record(self):
        if self.state != '自检完成':
            raise ValidationError(f"工单状态为{self.state}，禁止报工")
        base_url = self.env['ir.config_parameter'].get_param('web.base.url')
        result = requests.post(
            f"{base_url}/roke/wylj/create_work_record_schedule",
            data=json.dumps({"apikey": self.equipment_id.apikey}),
            headers={"Content-Type": "application/json"}
        )
        if result.status_code == 200:
            result = json.loads(result.content).get("result")
            if result.get("state") == "error":
                raise UserError(result.get("msgs"))



