<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- tree -->
    <record id="view_wylj_roke_mes_equipment_tree" model="ir.ui.view">
        <field name="name">view_wylj_roke_mes_equipment_tree</field>
        <field name="model">roke.mes.equipment</field>
        <field name="arch" type="xml">
            <tree string="设备">
                <field name="code" optional="show"/>
                <field name="name" optional="show"/>
                <field name="asset_number" optional="show"/>
                <field name="specification" optional="show"/>
                <field name="workshop_id" optional="show"/>
                <field name="e_state" optional="show"/>
                <field name="archives_date" optional="show"/>
                <field name="category_id" optional="hide"/>
                <field name="spot_plan_ids" optional="hide"/>
                <field name="location" optional="hide"/>
                <field name="mold_id" optional="hide"/>
                <field name="manufacturer" optional="hide"/>
                <field name="manufacture_date" optional="hide"/>
                <field name="warranty_date" optional="hide"/>
                <field name="create_archives_user_id" optional="hide"/>
                <field name="archives_code" optional="hide"/>
                <field name="maintenance_scheme_id" optional="hide"/>
                <field name="maintenance_frequency" optional="hide"/>
                <field name="maintenance_interval" optional="hide"/>
            </tree>
        </field>
    </record>
    <!-- form -->
    <record id="view_wylj_roke_mes_equipment_form" model="ir.ui.view">
        <field name="name">view_wylj_roke_mes_equipment_form</field>
        <field name="model">roke.mes.equipment</field>
        <field name="priority" eval="15"/>
        <field name="arch" type="xml">
            <form string="设备">
                <header>
                    <!--                    <button name="repair_request" type="object" string="报修" class="oe_highlight"-->
                    <!--                            attrs="{'invisible':['|', ('in_repair', '=', True), ('e_state', '=', '报废')]}"/>-->
                    <button name="btn_cr_qr_image" type="object" string="生成二维码" class="oe_highlight"/>
                    <button name="data_acquisition_pull" type="object" string="数采拉取" class="oe_highlight"/>
                    <button name="action_scrap_equipment" type="object" string="设备报废"
                            class="btn-danger" confirm="确定要将此设备报废吗？报废后设备将无法使用。"
                            attrs="{'invisible': [('e_state', '=', '报废')]}"/>
                    <button name="action_cancel_scrap_equipment" type="object" string="取消报废"
                            class="btn-warning" confirm="确定要取消此设备的报废状态吗？"
                            attrs="{'invisible': [('e_state', '!=', '报废')]}"/>
                    <field name="e_state" widget="statusbar"/>
                </header>
                <!--                <sheet>-->
                <group col="8">
                    <field name="name"/>
                    <field name="category_id"/>
                    <field name="spot_plan_ids"/>
                    <field name="maintenance_scheme_id"/>
                    <field name="code" required="1"/>
                    <field name="location"/>
                    <field name="mold_id"/>
                    <field name="user_id"/>
                </group>
                <group>
                    <!--                        <group>-->
                    <!--                            <field name="qr_code"/>-->
                    <!--                        </group>-->
                    <group col="4">
                        <field name="maintain_user_ids" widget="many2many_tags"/>
                        <field name="item_line_id"/>
                        <field name="create_date" invisible="1"/>
                    </group>
                </group>
                <notebook>
                    <page string="基础信息">
                        <group col="8">
                            <field name="specification"/>
                            <!--                                <field name="index_code"/>-->
                            <field name="maintenance_interval"/>
                            <field name="maintenance_frequency"/>
                            <field name="manufacturer"/>
                            <field name="manufacture_date" string="到场日期"/>
                            <!--                                <field name="warranty_date"/>-->
                            <!--                                <field name="register_code"/>-->
                            <!--                                <field name="test_org_id"/>-->
                            <!--                                <field name="archives_code"/>-->
                            <!--                                <field name="create_archives_user_id"/>-->
                            <field name="asset_number"/>
                            <field name="work_team_id"/>
                            <field name="workshop_id"/>
                            <field name="use_permit_code"/>
                            <field name="roke_company_id" string="当前基地"
                                   context="{'tree_view_ref': 'roke_mes_wylj.view_readonly_roke_company_tree'}"/>
                            <field name="current_location_id"/>
                            <field name="apikey"/>
                            <!--                                <field name="kq_location_id"/>-->
                            <!--                                <field name="kw_location_id"/>-->
                        </group>
                        <group>
                            <field name="qr_image" widget="image" readonly="1" img_width="128" img_height="128"
                                   height="128"/>
                        </group>
                        <field name="note" placeholder="此处录入设备描述或内部备注"/>
                    </page>
                    <page string="维修记录">
                        <field name="repair_ids" readonly="1">
                            <tree>
                                <field name="code"/>
                                <field name="report_user_id"/>
                                <field name="report_time"/>
                                <field name="fault_description"/>
                                <field name="user_id"/>
                                <field name="repair_user_id"/>
                                <field name="state"/>
                            </tree>
                            <form>
                                <sheet>
                                    <div class="oe_title">
                                        <label for="code" class="oe_edit_only"/>
                                        <h1>
                                            <field name="code" readonly="1"/>
                                        </h1>
                                    </div>
                                    <field name="type" invisible="1"/>
                                    <group>
                                        <group>
                                            <field name="equipment_id" required="1" options="{'no_create': True}"/>
                                            <field name="user_id" widget="many2many_tags"
                                                   options="{'no_create': True}"/>
                                            <field name="report_user_id" required="1" options="{'no_create': True}"/>
                                            <field name="report_time" required="1"/>
                                        </group>
                                        <group>
                                            <field name="fault_description"/>
                                            <field name="fault_files" widget="many2many_binary" string="添加故障照片"
                                                   nolabel="1" colspan="2"/>
                                        </group>
                                    </group>
                                    <notebook>
                                        <page string="维修结果">
                                            <group>
                                                <group>
                                                    <field name="repair_user_id" readonly="1"/>
                                                </group>
                                                <group>
                                                    <field name="repair_description" readonly="1"/>
                                                    <field name="repair_files" readonly="1" widget="many2many_binary"/>
                                                </group>
                                            </group>
                                            <field name="postpone_description" placeholder="此处录入延期说明"
                                                   readonly="1"/>
                                            <field name="cancel_description" placeholder="此处录入取消说明"
                                                   readonly="1"/>
                                        </page>
                                    </notebook>
                                </sheet>
                            </form>
                        </field>
                    </page>
                    <page string="点检记录">
                        <field name="check_record_ids"/>
                    </page>
                    <page string="保养记录">
                        <field name="maintenance_ids" readonly="1">
                            <tree>
                                <field name="code"/>
                                <field name="maintenance_scheme_id"/>
                                <field name="last_maintenance_date"/>
                                <field name="user_id"/>
                                <field name="state"/>
                            </tree>
                            <form>
                                <sheet>
                                    <div class="oe_title">
                                        <label for="code" class="oe_edit_only"/>
                                        <h1>
                                            <field name="code" readonly="1"/>
                                        </h1>
                                    </div>
                                    <field name="type" invisible="1"/>
                                    <group>
                                        <group>
                                            <field name="equipment_id" options="{'no_open': True}"/>
                                            <field name="user_id" options="{'no_open': True}"/>
                                        </group>
                                        <group>
                                            <field name="maintenance_scheme_id" options="{'no_create': True}"/>
                                            <field name="last_maintenance_date"/>
                                        </group>
                                    </group>
                                    <notebook>
                                        <page string="保养项目">
                                            <field name="item_ids">
                                                <tree editable="bottom">
                                                    <field name="order_id" invisible="1"/>
                                                    <field name="item_id"/>
                                                    <field name="execute_user_id" readonly="1"/>
                                                    <field name="execute_time" readonly="1"/>
                                                    <field name="execute_files" readonly="1"/>
                                                    <field name="description" readonly="1"/>
                                                    <field name="state"/>
                                                </tree>
                                            </field>
                                            <field name="postpone_description" placeholder="此处录入延期说明"/>
                                            <field name="cancel_description" placeholder="此处录入取消说明"/>
                                        </page>
                                    </notebook>
                                </sheet>
                            </form>
                        </field>
                    </page>
                    <page string="数采检查项">
                        <field name="item_line_ids" readonly="1">
                            <tree>
                                <field name="item_id"/>
                                <field name="item_name"/>
                            </tree>
                        </field>
                    </page>
                    <!--                        <page string="更换件记录">-->
                    <!--                            <field name="change_record_ids">-->
                    <!--                                <tree editable="bottom">-->
                    <!--                                    <field name="sequence" widget="handle"/>-->
                    <!--                                    <field name="name"/>-->
                    <!--                                    <field name="new_name"/>-->
                    <!--                                    <field name="record_date"/>-->
                    <!--                                    <field name="company_id" groups="base.group_multi_company" optional="hide"/>-->
                    <!--                                </tree>-->
                    <!--                            </field>-->
                    <!--                        </page>-->
                </notebook>
                <!--                </sheet>-->
            </form>
        </field>
    </record>

    <!--action-->
    <record id="roke_mes_equipment.view_roke_mes_equipment_action" model="ir.actions.act_window">
        <field name="name">设备</field>
        <field name="res_model">roke.mes.equipment</field>
        <field name="view_mode">tree,form,pivot</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('roke_mes_wylj.view_wylj_roke_mes_equipment_tree')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('roke_mes_wylj.view_wylj_roke_mes_equipment_form')})]"/>
    </record>

    <!--action-->
    <record id="view_readonly_roke_mes_equipment_action" model="ir.actions.act_window">
        <field name="name">设备</field>
        <field name="res_model">roke.mes.equipment</field>
        <field name="view_mode">tree,form,pivot</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">
            {'create': False, 'edit': False, 'delete': False}
        </field>
        <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('roke_mes_wylj.view_wylj_roke_mes_equipment_tree')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('roke_mes_wylj.view_wylj_roke_mes_equipment_form')})]"/>
    </record>

    <menuitem id="readonly_roke_mes_equipment_menu" name="设备" sequence="15"
              parent="roke_mes_equipment.roke_mes_equipment_manage_menu"
              action="view_readonly_roke_mes_equipment_action"/>

    <record id="inherit_view_roke_mes_eqpt_spot_check_plan_form" model="ir.ui.view">
        <field name="name">inherit.roke.mes.eqpt.spot.check.plan.form</field>
        <field name="model">roke.mes.eqpt.spot.check.plan</field>
        <field name="inherit_id" ref="roke_mes_equipment.view_roke_mes_eqpt_spot_check_plan_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='check_item_ids']" position="replace">
                <field name="item_ids">
                    <tree editable="bottom">
                        <field name="item_id"/>
                        <field name="method"/>
                        <field name="standard"/>
                    </tree>
                </field>
            </xpath>
            <xpath expr="//field[@name='equipment_id']" position="replace">
                <field name="name" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='name']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>
    <record id="inherit_view_roke_mes_eqpt_spot_check_plan_tree" model="ir.ui.view">
        <field name="name">inherit.roke.mes.eqpt.spot.check.plan.tree</field>
        <field name="model">roke.mes.eqpt.spot.check.plan</field>
        <field name="inherit_id" ref="roke_mes_equipment.view_roke_mes_eqpt_spot_check_plan_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='description']" position="after">
                <field name="item_ids" widget="many2many_tags"/>
            </xpath>
        </field>
    </record>
</odoo>