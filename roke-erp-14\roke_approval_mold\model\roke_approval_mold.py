# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.inspur.com
"""
from odoo import models, fields, api, _
from odoo.addons.roke_all_approval.model.roke_all_approval import ALLOW_APPROVAL

# 报废申请单
class RokeMoldScrappingRequest(models.Model):
    _name = "roke.mold.scrapping.request"
    _inherit = ["approval.from", "mail.thread", "mail.activity.mixin", "roke.mold.scrapping.request"]
    _rec_name = "code"

ALLOW_APPROVAL.update({"roke.mold.scrapping.request": "roke_mes_sjzzh"})

# 封存申请单
class RokeMoldSealedRequest(models.Model):
    _name = "roke.mold.sealed.request"
    _inherit = ["approval.from", "mail.thread", "mail.activity.mixin", "roke.mold.sealed.request"]
    _rec_name = "code"

ALLOW_APPROVAL.update({"roke.mold.sealed.request": "roke_mes_sjzzh"})

# 请验单
class RokePleaseCheckOrder(models.Model):
    _name = "roke.please.check.order"
    _inherit = ["approval.from", "roke.please.check.order"]
    _rec_name = "code"

ALLOW_APPROVAL.update({"roke.please.check.order": "roke_mes_sjzzh"})

# 质检单
class RokeQualityOrder(models.Model):
    _name = "roke.quality.order"
    _inherit = ["approval.from", "mail.thread", "roke.quality.order"]
    _rec_name = "code"

ALLOW_APPROVAL.update({"roke.quality.order": "roke_mes_quality_enterprise"})


class InheritApprovalFlow(models.Model):
    _inherit = 'approval.flow'

    def _compute_domain(self):
        manual_models = [model.model for model in self.env['ir.model'].search([
            ('state', '=', 'manual'), ('transient', '!=', True)
        ])]
        # 处理卸载之后过滤已有数据
        ALLOW_MODEL = []
        model_obj = self.env['ir.model']
        for model in ALLOW_APPROVAL:
            model_id = model_obj.sudo().search([('model', '=', model)], limit=1)
            if model_id:
                if ALLOW_APPROVAL[model] in model_id.modules:
                    ALLOW_MODEL.append(model)
        return [('model', 'in', ALLOW_MODEL + manual_models)]


    model_id = fields.Many2one('ir.model', u'模型', domain=_compute_domain, index=1)