from odoo import api, fields, models

class RokeEquipmentProductionCapacitySchedule(models.Model):
    _name = 'roke.equipment.production.capacity.schedule'
    _description = '设备产能表'
    equipment_id = fields.Many2one("roke.mes.equipment", string='设备名称')
    note = fields.Text(string='备注')
    line_ids = fields.One2many('roke.equipment.production.capacity.schedule.line', 'schedule_id', string='明细')

class RokeEquipmentProductionCapacityScheduleLine(models.Model):
    _name = 'roke.equipment.production.capacity.schedule.line'
    _description = '设备产能明细表'

    product_id = fields.Many2one('roke.product', string='产品')
    product_code = fields.Char(related='product_id.code', string='产品编号', store=True)
    product_features = fields.Many2one(related='product_id.product_features',string='产品属性', store=True)
    product_category = fields.Many2one(related='product_id.category_id', string='产品类别', store=True)
    production_per_mold = fields.Float(string='每模生产数', digits=(16, 2))
    equipment_capacity = fields.Float(string='设备产能', digits=(16, 2))
    mold_id = fields.Many2one('roke.mold.ledger', string='模具')
    tool_id = fields.Many2one('roke.mes.tool', string='工装')
    scrap_rate = fields.Float(string='废品率(%)', digits=(5, 2))
    loss_rate = fields.Float(string='损耗率(%)', digits=(5, 2))
    safety_stock = fields.Float(string='安全库存', digits=(16, 2))

    schedule_id = fields.Many2one('roke.equipment.production.capacity.schedule', string='产能表')
    max_debug_times = fields.Integer(string='最大调试次数')
