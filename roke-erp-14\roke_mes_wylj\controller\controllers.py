# -*- coding: utf-8 -*-
from odoo import http, fields, SUPERUSER_ID
import logging
import math
from odoo.addons.roke_mes_base.tools import http_tool
from odoo.addons.roke_mes_equipment.controller import client_api
from odoo.exceptions import UserError
from odoo.addons.roke_mes_client.controller import login as mes_login
import datetime
import requests

_logger = logging.getLogger(__name__)


def get_repair_origin(value):
    if value == 'normal':
        return '日常报修'
    elif value == 'maintain':
        return '维保报修'
    elif value == 'check':
        return '巡检报修'
    else:
        return ''


def get_repair_operation_domain(user_id):
    """
    根据用户权限返回domain
        管理员、派工人员：可查看所有的
        作业人员：可查看派工给自己的
    :param user_id: 用户ID
    :return:
    """
    user = http.request.env(user=SUPERUSER_ID)['res.users'].search([('id', '=', user_id)])
    # 查找设备管理员权限组
    equipment_manager_group = http.request.env(user=SUPERUSER_ID)['res.groups'].search([('name', '=', '设备管理员')])
    # 查找设备派工人员权限组
    equipment_dispatch_group = http.request.env(user=SUPERUSER_ID)['res.groups'].search([('name', '=', '设备派工人员')])
    # 有设备管理员权限，可以查看所有单据
    if equipment_manager_group.id in user.groups_id.ids or equipment_dispatch_group.id in user.groups_id.ids:
        # return [("state", "in", ['wait', 'assign', 'postpone'])]
        return []
    # 查找设备作业人员权限组
    equipment_operation_group = http.request.env(user=SUPERUSER_ID)['res.groups'].search(
        [('name', '=', '设备作业人员')])
    # 有设备作业人员权限，可以查看派工给自己的单据
    if equipment_operation_group.id in user.groups_id.ids:
        # return [('user_id', '=', user_id), ("state", "in", ['assign', 'postpone'])]
        return [('user_id', '=', user_id)]
    else:
        return [('id', '=', 0)]


def get_plan_name(value):
    if value == 'period':
        return '定期检查'
    elif value == 'start':
        return '开班检查'
    elif value == 'random':
        return '随机检查'
    else:
        return ''


def get_value_type_name(value):
    if value == 'text':
        return '文本'
    elif value == 'float':
        return '小数'
    elif value == 'int':
        return '整数'
    elif value == 'select':
        return '选择'
    else:
        return ''


def get_user_role(user_id):
    user = http.request.env(user=SUPERUSER_ID)['res.users'].search([('id', '=', user_id)])
    role = ''

    for user_group in user.groups_id:
        if user_group.name == '设备维修人员':
            role = '设备维修人员'
            break
        elif user_group.name == '设备组长':
            role = '设备组长'
            break
        elif user_group.name == '设备一线员工':
            role = '设备一线员工'
            break
        elif user_group.name == '设备点检':
            role = '设备点检'
            break
        else:
            role = ''
    return role


class Wylj(client_api.ClientApiEquipment):

    @http.route('/wylj/get/user_list', type='json', auth='user', cors='*', csrf=False)
    def get_user_list(self, **kw):
        """获取用户"""
        name = http.request.jsonrequest.get('name')
        user_id = http.request.env.user.id
        type = http.request.jsonrequest.get('type')
        domain = []
        if name:
            domain = [("login", "ilike", name)]
        if type:
            domain.append(('type', '=', type))
        domain.append(('team_leader_user_id', '=', user_id))
        try:
            user_ids = http.request.env['repair.team.schedule'].search(domain, limit=1)
            user_list = []

            for user_id in user_ids.schedule_line_ids:
                user_list.append({
                    "id": user_id.user_id.id,
                    "name": user_id.user_id.name,
                    "user_id": user_id.user_id.id,
                    "user_name": user_id.user_id.name,
                })
            result = {'state': 'success', 'msgs': '获取信息成功', 'data': user_list}
        except Exception as e:
            result = {'state': 'error', 'msgs': e}
        return result

    @http.route('/wylj/set/maintenance_finish', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False,
                cors='*')
    def set_maintenance_finish(self):
        """
        设备维修单完成
        状态改变
        """
        order_id = http.request.jsonrequest.get('order_id')
        try:
            repairs_obj = http.request.env["roke.mes.maintenance.order"]
            repair_id = repairs_obj.sudo().search([('id', '=', order_id)])
            if not repair_id:
                raise UserError('单据不存在！')
            # if repair_id.deadline:
            #     deadline_date = fields.Date.from_string(repair_id.deadline)
            #     current_date = fields.Date.from_string(fields.Date.today())
            #     if deadline_date < current_date:
            #         return {'state': 'error', 'msgs': "超出最后期限"}
            repair_id.state = "finish"
            repair_id.equipment_id.e_state = "闲置"  # 设备维修单完成后修改设备状态为闲置
            repair_id.end_time = fields.Datetime.now()
            result = {'state': 'success', 'msgs': '转委外成功'}
        except Exception as e:
            result = {'state': 'error', 'msgs': e}
        return result

    @http.route('/wylj/set/maintenance_outsourcing', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False,
                cors='*')
    def set_maintenance_outsourcing(self):
        """
        设备维修单转委外
        状态改变
        """
        order_id = http.request.jsonrequest.get('order_id')
        try:
            repairs_obj = http.request.env["roke.mes.maintenance.order"]
            repair_id = repairs_obj.sudo().search([('id', '=', order_id)])
            if not repair_id:
                raise UserError('单据不存在！')
            repair_id.state = "outsourcing"
            result = {'state': 'success', 'msgs': '转委外成功'}
        except Exception as e:
            result = {'state': 'error', 'msgs': e}
        return result

    @http.route('/roke/get_start_repairs', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False,
                cors='*')
    def get_start_repairs(self):
        """
        设备维修单开始维修
        点击开始维修
        获取时间
        状态改变
        """
        order_id = http.request.jsonrequest.get('order_id')
        try:
            repairs_obj = http.request.env["roke.mes.maintenance.order"]
            repair_id = repairs_obj.sudo().search([('id', '=', order_id)])
            if not repair_id:
                raise UserError('单据不存在！')
            repair_id.state = "assign"
            repair_id.start_time = fields.Datetime.now()
            repair_id.repair_user_id = http.request.env.user.id
            repair_id.user_id = [http.request.env.user.id]
            result = {'state': 'success', 'msgs': '改变成功'}
        except Exception as e:
            result = {'state': 'error', 'msgs': e}
        return result

    @http.route('/roke/equipment_conversion_id', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def equipment_conversion_id(self):
        """
        设备编号转id
        :return:
        """
        code = http.request.jsonrequest.get('code', '')
        try:
            if not code:
                raise UserError('设备编号为空！')
            equipment_id = http.request.env["roke.mes.equipment"].search(
                ['|', ('code', '=', code), ('qr_code', '=', code)], limit=1)
            if not equipment_id:
                raise UserError('设备编号【%s】在系统找不到对应数据！' % code)
            data = []
            for equipment in equipment_id:
                data.append({
                    'user_id': equipment.user_id.id or '',
                    'user_name': equipment.user_id.name or '',
                    'location': equipment.location or '',
                })
            result = {'state': 'success', 'msgs': '转换成功', 'id': equipment_id.id, 'name': equipment_id.name or '',
                      'code': equipment_id.code,
                      'data': data}
        except Exception as e:
            result = {'state': 'error', 'msgs': e}
        return result

    # 更新派工人员
    @http.route('/roke/update_maintenance_employee', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def update_maintenance_employee(self):
        user_ids = http.request.jsonrequest.get('user_ids', [])
        order_id = http.request.jsonrequest.get('order_id', False)
        try:
            if not user_ids:
                raise UserError('请选择指派人员！')
            if not order_id:
                raise UserError('维修任务id为空！')
            maintenance = http.request.env["roke.mes.maintenance.order"].browse(int(order_id))
            if not maintenance:
                raise UserError('维修任务不存在！')
            maintenance.write({'user_id': user_ids})
            result = {'state': 'success', 'msgs': '更换成功'}
        except Exception as e:
            result = {'state': 'error', 'msgs': e}
        return result

    def _get_repair_record_list(self, filter_param):
        """
        获取报修记录单据列表
        :return:
        """
        _logger.info(f"入参：{filter_param}")

        order_type = filter_param.get("order_type")
        if not order_type or order_type not in ("repair", "maintain"):
            return {"state": "error", "msgs": "单据类型错误，请联系后台相关人员"}
        order_id = filter_param.get("order_id", False)
        if order_id:
            domain = [("id", "=", order_id)]
        else:
            domain = [("type", "=", order_type)]
            code = filter_param.get("code", False)
            if code:
                domain.append("|")
                domain.append("|")
                domain.append(("equipment_id.qr_code", "ilike", code))
                domain.append(("code", "ilike", code))
                domain.append(("equipment_code", "ilike", code))
            state = filter_param.get("state", False)
            if state:
                if state in ["wait_assign", "assign"]:
                    domain.append(("is_assign", "=", state))
                else:
                    domain.append(("state", "=", state))
            report_time = filter_param.get("create_date")
            if report_time:
                domain.append(("report_time", "<=", report_time))
                domain.append(("report_time", ">=", report_time))
        limit = http.request.jsonrequest.get('limit', 10)
        page_number = http.request.jsonrequest.get('page_number', 1)
        result = []
        offset = (page_number - 1) * page_number
        page_orders = http.request.env['roke.mes.maintenance.order'].search(domain, order='id desc',
                                                                            limit=limit, offset=offset)
        for order in page_orders:
            r = client_api.get_repair_origin(order.repair_origin)
            # users_names = ""
            # for user in order.user_id:
            #     users_names += user.name + ","
            result.append({
                "id": order.id,
                "code": order.code or "",
                "equipment": {"e_id": order.equipment_id.id, "e_code": order.equipment_id.code or "",
                              "e_name": order.equipment_id.name or "", "e_location": order.e_location or ""},
                "assign_user": order.report_user_id.name or "",  # 报修人
                "assign_phone": order.report_user_id.phone or '',
                "state": http_tool.selection_to_dict("roke.mes.maintenance.order", "state")[
                    order.state] if order.state else "",
                "priority": http_tool.selection_to_dict("roke.mes.maintenance.order", "priority")[
                    order.priority] if order.priority else "",
                "deadline": order.deadline.strftime('%Y-%m-%d') if order.deadline else "",
                "create_date": order.create_date.strftime('%Y-%m-%d') if order.create_date else "",
                "repair_origin": r,
                "failure_id": order.failure_id.id,
                "failure_name": order.failure_id.note,
                "report_user_id": order.report_user_id.id,
                "report_user_name": order.report_user_id.name,
                "is_assign": order.is_assign,
                "user_names": ",".join(order.user_id.mapped("name")) if order.user_id else "",
            })
        total = http.request.env['roke.mes.maintenance.order'].search_count(domain)
        return_data = {
            "state": "success",
            "msgs": "获取成功",
            "records": result,
            "total": total,
            "page_total": math.ceil(total / limit),
            "limit": limit,
            "page_number": page_number
        }
        return return_data

    def _get_maintenance_order_list(self, filter_param):
        """
        获取维保单据列表
        :return:
        """
        _logger.info('--方法获取的参数--')
        _logger.info(filter_param)
        order_type = filter_param.get("order_type")
        if not order_type or order_type not in ("repair", "maintain"):
            return {"state": "error", "msgs": "单据类型错误，请联系后台相关人员"}
        domain = [("type", "=", order_type)]
        # 新增设备编号
        # if filter_param.get("equipment_code"):
        if filter_param.get("code"):
            domain.append("|")
            domain.append("|")
            domain.append(("code", "ilike", filter_param.get("code")))
            domain.append(("equipment_id.qr_code", "ilike", filter_param.get("code")))
            domain.append(("equipment_code", "ilike", filter_param.get("code")))
        if filter_param.get("equipment_id"):
            domain.append(("equipment_id", "=", int(filter_param.get("equipment_id"))))
        if filter_param.get("report_time"):
            domain.append(("report_time", "=", filter_param.get("report_time")))
        if filter_param.get("e_code"):
            domain.append("|")
            domain.append(("equipment_code", "=", filter_param.get("e_code")))
            domain.append(("equipment_id.qr_code", "=", filter_param.get("e_code")))
        # 保养任务
        if filter_param.get("create_date"):
            start_time = filter_param.get("create_date") + " 00:00:00"
            finish_time = filter_param.get("create_date") + " 23:59:59"
            domain.append(("create_date", ">", start_time))
            domain.append(("create_date", "<", finish_time))
        state = filter_param.get("state")
        if state:
            if state == 'assign':
                domain.append(("state", "in", ["assign", "wait"]))
            else:
                domain.append(("state", "=", state))
        # else:  # 没传状态就筛选待派工、已派工、延期的
        #     domain.append(("state", "in", ['wait', 'assign', 'postpone']))
        # if state == "未完成":
        #     domain.append(("state", "in", ("wait", "postpone", "assign")))
        # elif state == "待派工":
        #     domain.append(("state", "=", "wait"))
        # elif state == "延期":
        #     domain.append(("state", "=", "postpone"))
        # elif state == "完成":
        #     domain.append(("state", "=", "finish"))
        # elif state == "取消":
        #     domain.append(("state", "=", "cancel"))
        if order_type == "repair":
            # if filter_param.get("report_user"):
            #     domain.append(("report_user_id.name", "ilike", filter_param.get("report_user")))
            # if filter_param.get("repair_user"):
            #     domain.append(("repair_user_id.name", "ilike", filter_param.get("repair_user")))
            domain += get_repair_operation_domain(http.request.uid or http.request.session.uid)
        else:
            if filter_param.get("maintenance_scheme"):
                domain.append("|")
                domain.append(("maintenance_scheme_id.code", "ilike", filter_param.get("maintenance_scheme")))
                domain.append(("maintenance_scheme_id.name", "ilike", filter_param.get("maintenance_scheme")))
            # domain += get_repair_domain(http.request.uid or http.request.session.uid)
        limit = http.request.jsonrequest.get('limit', 50)
        page_number = http.request.jsonrequest.get('page_number', 1)
        result = []
        orders = http.request.env['roke.mes.maintenance.order'].search(domain, order='id desc')
        page_orders = orders[(page_number - 1) * limit: (page_number - 1) * limit + limit]
        _logger.info(page_orders)
        for order in page_orders:
            users_names = ""
            for user in order.user_id:
                users_names += user.name + ","
            r = get_repair_origin(order.repair_origin)
            result.append({
                "id": order.id,
                "code": order.code or "",
                "maintenance_type": "设备",
                "equipment": {"e_id": order.equipment_id.id, "e_code": order.equipment_id.code or "",
                              "e_name": order.equipment_id.name or "", "e_location": order.e_location or "未维护"},
                "assign_user": order.user_id.mapped("name") if order.user_id else "",
                "assign_phone": order.user_id.mapped("phone") if order.user_id else "",
                "state": http_tool.selection_to_dict("roke.mes.maintenance.order", "state")[
                    order.state] if order.state else "",
                "priority": http_tool.selection_to_dict("roke.mes.maintenance.order", "priority")[
                    order.priority] if order.priority else "",
                "deadline": order.deadline.strftime('%Y-%m-%d') if order.deadline else "",
                "create_date": order.create_date.strftime('%Y-%m-%d') if order.create_date else "",
                "repair_origin": r,
                "failure_id": order.failure_id.id,
                "failure_name": order.failure_id.note,
                "report_user_id": order.report_user_id.id,
                "report_user_name": order.report_user_id.name,
                "user_names": users_names,
            })
        return_data = {
            "state": "success",
            "msgs": "获取成功",
            "records": result,
            "total": len(orders),
            "page_total": math.ceil(len(orders) / limit),
            "limit": limit,
            "page_number": page_number
        }
        _logger.info('===返回的数据===')
        _logger.info(return_data)
        return return_data

    @http.route('/roke/equipment/failure_submit', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def failure_submit(self):
        """
        设备报修
        :param: code：报修编号
        :param: equipment_id：设备ID
        :param: description：故障描述
        :param: file_datas：上传照片
        :param: repair_origin：报修来源
        :param: failure_id：故障类型ID
        :param: phone：联系电话
        :param: e_location：设备地点
        :param: deadline：最后期限
        :return:
        """
        _logger.info("设备报修")
        equipment_id = http.request.jsonrequest.get('equipment_id', False)
        repair_origin = http.request.jsonrequest.get('repair_origin', False)
        e_location = http.request.jsonrequest.get('e_location', False)
        phone = http.request.jsonrequest.get('phone', False)
        failure_id = http.request.jsonrequest.get('failure_id', False)
        file_datas = http.request.jsonrequest.get("file_datas", [])
        picture = http.request.jsonrequest.get('picture', False)
        repair_user_id = http.request.jsonrequest.get('repair_user_id', False) or False

        if not http_tool.check_id_valid(equipment_id):
            return {"state": "error", "msgs": "缺少入参或入参格式错误"}
        # equipment = http.request.env["roke.mes.equipment"].browse(int(equipment_id))
        equipment = http.request.env["roke.mes.equipment"].search(
            [("id", "=", equipment_id), ("e_state", "!=", "报修")])
        if not equipment:
            return {"state": "error", "msgs": "设备不存在或已维修"}
        try:
            order = http.request.env["roke.mes.maintenance.order"].create({
                "equipment_id": equipment.id,
                "type": "repair",
                "fault_description": http.request.jsonrequest.get('description', ''),
                "priority": http.request.jsonrequest.get('priority', 'normal'),
                "deadline": http.request.jsonrequest.get('deadline') if http.request.jsonrequest.get(
                    'deadline') else False,
                "repair_origin": repair_origin or "normal",
                "failure_id": failure_id,
                "phone": phone,
                "e_location": e_location,
                "repair_user_id": repair_user_id  # 维修人
            })
            equipment.write({"in_repair": True, "e_state": "报修"})
            # 处理故障照片
            if file_datas:
                attachment_ids = http_tool.create_image_attachment("GZ" + order.code, file_datas)
                order.write({"fault_files": [(6, 0, attachment_ids)]})
            if picture:
                order.write({"picture": picture})
            try:
                order.send_wx_message_submit_repair()
            except Exception as e:
                _logger.info(e)
            return {"state": "success", "msgs": "报修成功", "repair_order_id": order.id}
        except Exception as e:
            _logger.error(e)
            return {"state": "error", "msgs": e}

    @http.route('/wylj/get/equipment_failure', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_equipment_failure(self):
        """获取设备故障数据库"""
        domain = []
        text = http.request.jsonrequest.get('text', False)
        page_size = int(http.request.jsonrequest.get('page_size', 10))
        page_no = int(http.request.jsonrequest.get('page_no', 1))
        if text:
            domain = ["|", ("code", "ilike", text), ("name", "ilike", text)]
        offset = (page_no - 1) * page_size
        failure_ids = http.request.env["equipment.failure"].search(domain, limit=page_size, offset=offset)
        failure_list = []
        for item in failure_ids:
            failure_list.append({
                "id": item.id,
                "code": item.code,
                "name": item.note,
                "note": item.name
            })
        return {"code": 0, "msgs": "获取成功", "data": failure_list}

    @http.route('/roke/wylj/get_equipment', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_equipment(self):
        """获取设备"""
        code = http.request.jsonrequest.get('code', False)
        role = get_user_role(http.request.env.user.id)
        if not role in ['设备维修人员', '设备组长', '设备一线员工', '设备点检']:
            return {"state": "error",
                    "msgs": "当前用户不在权限组['设备维修人员', '设备组长', '设备一线员工', '设备点检']当中"}
        equipment_obj = http.request.env["roke.mes.equipment"]

        equipment = equipment_obj.search([('qr_code', '=', code)])
        if not equipment:
            _logger.error(f"设备不存在,code:{code}")
            return {"state": "error", "msgs": "设备不存在"}
        # if not equipment.spot_plan_ids:
        #     return {"state": "error", "msgs": "该设备未配置点检方案"}
        _plan_dict = {
            "id": equipment.id,
            "code": equipment.code,
            "name": equipment.name,
            "spot_plan_name": equipment.spot_plan_ids.name,
            "spot_plan_id": equipment.spot_plan_ids.id,
            "spot_plan_code": equipment.spot_plan_ids.code,
            "line_list": [],
            "category_id": equipment.category_id.id,  # 类别id
            "category_name": equipment.category_id.name,  # 类别名称
            "maintenance_scheme_id": equipment.maintenance_scheme_id.id,  # 保养方案id
            "maintenance_scheme_name": equipment.maintenance_scheme_id.name,  # 保养方案名称
            "location": equipment.location,  # 位置
            "maintain_user_ids": ", ".join(equipment.maintain_user_ids.mapped("name")),  # 保养人名字拼接
            "specification": equipment.specification,  # 规格
            "maintenance_interval": equipment.maintenance_interval,  # 保养周期
            "maintenance_frequency": equipment.maintenance_frequency,  # 保养频率
            "asset_number": equipment.asset_number,  # 资产编号
            "manufacturer": equipment.manufacturer,  # 厂家
            "roke_company_id": equipment.roke_company_id.id,  # 当前基地id
            "roke_company_name": equipment.roke_company_id.name,  # 当前基地
            "current_location_id": equipment.current_location_id.id,  # 当前仓库id
            "current_location_name": equipment.current_location_id.name,  # 当前仓库

            "manufacture_date": equipment.manufacture_date.strftime('%Y-%m-%d') if equipment.manufacture_date else "",
            # 到场日期
        }
        # scheme_id = http.request.jsonrequest.get('scheme_id')
        # plan_id = http.request.jsonrequest.get('plan_id')
        # plan_obj = http.request.env['roke.mes.eqpt.spot.check.record']
        # domain_list = [('check_plan_id', '=', scheme_id),
        #                ('check_plan_id.type', '=', 'period'),
        #                ('state', '!=', 'finish')]
        # # 中汇-新增待产
        # field = http.request.env(user=SUPERUSER_ID)['ir.model.fields'].search(
        #     [('model_id.model', '=', 'roke.mes.eqpt.spot.check.record'), ('name', '=', 'is_labor')])
        # if field:
        #     domain_list.append(('is_labor', '=', False))
        # _plan = plan_obj.search(domain_list,
        #                         order="create_date desc", limit=1)
        # if not _plan:
        #     return {"state": "error", "msgs": "已无可提交的巡检单"}
        # else:
        #     if (_plan.create_date + datetime.timedelta(hours=8)).date() != (
        #             datetime.datetime.now() + datetime.timedelta(hours=8)).date():
        #         return {"state": "error", "msgs": "今天已无可提交的巡检单"}
        # _plan_dict = dict()
        # if _plan:
        #     _plan_dict = {
        #         "id": _plan.id or "",
        #         "code": _plan.code or "",
        #         "equipment": {"e_id": _plan.equipment_id.id,
        #                       "e_code": _plan.equipment_id.code or "",
        #                       "e_name": _plan.equipment_id.name or "",
        #                       },
        #         "state": _plan.state,
        #         "state_name": get_state_name(_plan.state),
        #         "create_date": _plan.create_date.strftime('%Y-%m-%d') if _plan.create_date else "",
        #         "picture": _plan.picture,
        #         "is_labor": _plan.is_labor,
        #     }
        #     line_list = []
        #
        #     for line in _plan.item_record_ids:
        #         value_type = get_value_type_name(line.input_type_id.input_type)
        #         # 选择项
        #         select_item = []
        #         for item in line.check_item_id.select_item_ids:
        #             select_item.append({
        #                 "value": item.value
        #             })
        #         # 选择项
        #         line_list.append({
        #             "id": line.id,
        #             "name": line.check_item_id.name,
        #             "description": line.description or "",
        #             "value_type": value_type,
        #             "standard_value": line.standard_value or "",
        #             "select_item": select_item
        #         })
        #     _plan_dict.update({"line_list": line_list})
        # else:
        # scheme_obj = http.request.env['roke.mes.eqpt.spot.check.plan'].search([('id', '=', scheme_id)]
        line_list = []
        for line in equipment.spot_plan_ids.item_ids:
            value_type = get_value_type_name(line.item_id.input_type)
            # 选择项
            select_item = []
            for item in line.item_id.select_item_ids:
                select_item.append({
                    "value": item.value
                })
            # 选择项
            line_list.append({
                "id": line.item_id.id,
                "name": line.item_id.name,
                "description": line.method or "",
                "value_type": value_type,
                "standard_value": line.standard or "",
                "select_item": select_item
            })
        _plan_dict.update({"line_list": line_list})
        return {"state": "success", "msgs": "查询成功", "records": _plan_dict}

    @http.route('/roke/wylj/get_equipment_record', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_equipment_record(self):
        """获取设备"""
        code = http.request.jsonrequest.get('code', False)
        role = get_user_role(http.request.env.user.id)
        if not role in ['设备维修人员', '设备组长', '设备一线员工', '设备点检']:
            return {"state": "error",
                    "msgs": "当前用户不在权限组['设备维修人员', '设备组长', '设备一线员工', '设备点检']当中"}
        equipment_obj = http.request.env["roke.mes.equipment"]

        equipment = equipment_obj.search([('qr_code', '=', code)])
        if not equipment:
            _logger.error(f"设备不存在,code:{code}")
            return {"state": "error", "msgs": "设备不存在"}
        if not equipment.spot_plan_ids:
            return {"state": "error", "msgs": "该设备未配置点检方案"}
        _plan_dict = {
            "id": equipment.id,
            "code": equipment.code,
            "name": equipment.name,
            "spot_plan_name": equipment.spot_plan_ids.name,
            "spot_plan_id": equipment.spot_plan_ids.id,
            "spot_plan_code": equipment.spot_plan_ids.code,
            "line_list": []
        }
        line_list = []
        for line in equipment.spot_plan_ids.item_ids:
            value_type = get_value_type_name(line.item_id.input_type)
            # 选择项
            select_item = []
            for item in line.item_id.select_item_ids:
                select_item.append({
                    "value": item.value
                })
            # 选择项
            line_list.append({
                "line_id": line.id,
                "id": line.item_id.id,
                "name": line.item_id.name,
                "description": line.method or "",
                "value_type": value_type,
                "standard_value": line.standard or "",
                "select_item": select_item
            })
        _plan_dict.update({"line_list": line_list})
        return {"state": "success", "msgs": "查询成功", "records": _plan_dict}

    # @http.route('/roke/wylj/create_check_record', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
    #             cors='*')
    # def create_check_record(self):
    #     """创建点检记录"""
    #     id = http.request.jsonrequest.get('id', False)  # 设备id
    #     spot_plan_id = http.request.jsonrequest.get('spot_plan_id', False) # 点检方案id
    #     finish_time = http.request.jsonrequest.get('finish_time', False) # 点检完成时间
    #     check_item = http.request.jsonrequest.get('check_item', False) # 点检项
    #     picture = http.request.jsonrequest.get('picture', False) # 点检照片
    #     check_record_obj = http.request.env['roke.mes.eqpt.spot.check.record'] # 点检记录模型
    #
    #     if not all([id, spot_plan_id, finish_time, check_item]):
    #         return {"state": "error", "msgs": "缺少入参或入参格式错误"}
    #     try:
    #         check_record = check_record_obj.create({
    #             "equipment_id": id,
    #             "check_plan_id": spot_plan_id,
    #             "finish_time": finish_time,
    #             "finish_user_id": http.request.env.user.id,
    #         })
    #         check_item_dict = {}
    #         for check in check_item:
    #             check_item_dict[check["name"]] = check
    #         if check_record:
    #             # 按钮执行点检项目
    #             for item in check_record.item_record_ids:
    #                 if item.check_item_id.name in check_item_dict:
    #                     wizard = http.request.env["roke.execute.check.item.wizard"].create({
    #                         "check_item_id": item.id,
    #                         "target_state": check_item_dict[item.check_item_id.name].get("target_state"),
    #                         "check_value": check_item_dict[item.check_item_id.name].get("description"),
    #                     })
    #                     wizard.api_confirm()
    #                 else:
    #                     return {"state": "error", "msgs": f"点检项【{item.check_item_id.name}】不存在"}
    #             # check_record.write({"state": "finish"})
    #             check_record.make_finish()
    #             return {"state": "success", "msgs": "点检成功", "data": check_record.id}
    #         else:
    #             return {"state": "error", "msgs": "点检失败"}
    #         # return {"state": "error", "msgs": "点检失败"}
    #     except Exception as e:
    #         _logger.error(e)
    #         return {"state": "error", "msgs": f"点检记录创建失败，具体原因入下：{e}"}

    @http.route('/roke/mes/submit_check_order_create', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def submit_check_order_create(self):
        """
        提交巡检单
        :return:
        """
        item_list = http.request.jsonrequest.get('item_list', False)
        check_plan_id = http.request.jsonrequest.get('check_plan_id', False)
        equipment_id = http.request.jsonrequest.get('equipment_id', False)
        picture = http.request.jsonrequest.get('picture', False)
        description = http.request.jsonrequest.get('description', False)
        finish_time = http.request.jsonrequest.get('finish_time', False)
        user_id = http.request.uid or http.request.session.uid
        request = http.request.env(user=user_id)
        if not item_list:
            return {"state": "error", "msgs": "参数错误"}
        # 创建巡检记录
        order_obj = request['roke.mes.eqpt.spot.check.record'].create({
            "check_plan_id": int(check_plan_id),
            "equipment_id": int(equipment_id),
            "picture": picture,
            "state": "finish",
            "finish_user_id": user_id,
            "finish_time": finish_time,
            "description": description,
            "is_api": True
        })
        # 创建巡检明细
        for item in item_list:
            request['roke.mes.eqpt.spot.check.line'].create({
                "record_id": order_obj.id,
                "check_item_id": item.get('check_item_id'),
                "check_value": item.get('check_value'),
            })
        return {"state": "success", "msgs": "提交成功"}

    @http.route('/roke/mes/get_check_list', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False, cors='*')
    def get_check_list(self):
        """
        获取巡检记录
        :return:
        """
        create_date = http.request.jsonrequest.get('create_date', False)
        state = http.request.jsonrequest.get('state', False)
        code = http.request.jsonrequest.get('code', False)
        equipment_code = http.request.jsonrequest.get('equipment_code', '')
        domain_list = []
        # 中汇-新增待产
        field = http.request.env(user=SUPERUSER_ID)['ir.model.fields'].search(
            [('model_id.model', '=', 'roke.mes.eqpt.spot.check.record'), ('name', '=', 'is_labor')])
        if field:
            domain_list.append(('is_labor', '=', False))
        if code:
            domain_list.append(('code', 'ilike', code))
        if create_date:
            start_time = create_date + " 00:00:00"
            finish_time = create_date + " 23:59:59"
            domain_list.append(("create_date", ">", start_time))
            domain_list.append(("create_date", "<", finish_time))
        if state:
            domain_list.append(('state', '=', state))
        if equipment_code:
            domain_list.append(('equipment_id.code', '=', equipment_code))
        res = http.request.env(user=SUPERUSER_ID)['roke.mes.eqpt.spot.check.record'].search(domain_list)
        # 处理分页
        page_no = http.request.jsonrequest.get('page_no', 1)  # 页码
        page_size = http.request.jsonrequest.get('page_size', 10)  # 每页记录数
        total_number = len(res)
        if page_size:
            total_page = math.ceil(len(res) / page_size)  # 总页数
            res = res[(page_no - 1) * page_size: page_no * page_size]  # 当前页记录
        else:
            total_page = 0
        data_list = []
        for record in res:
            if record.state == 'wait':
                state = '等待'
            elif record.state == 'finish':
                state = '完成'
            elif record.state == 'cancel':
                state = '取消'
            else:
                state = ''

            data_list.append({
                "id": record.id,
                "scheme_id": record.check_plan_id.id,
                "code": record.code,
                "name": record.check_plan_id.name,
                "equipment_id": record.equipment_id.id or "",
                "equipment_code": record.equipment_id.code or "",
                "equipment_name": record.equipment_id.name or "",
                "equipment_location": record.equipment_id.location or "",
                "create_date": record.create_date.date(),
                "state": state,
                "plan_type": record.check_plan_id.type,
                "plan_type_name": get_plan_name(record.check_plan_id.type),
                "operator": record.finish_user_id.id,
                "operator_name": record.finish_user_id.name
            })
        return {"state": "success", "msgs": "获取成功", "records": data_list,
                "page_no": page_no, "total_number": total_number, "total_page": total_page}

    # 获取设备保养方案
    @http.route('/roke/get_maintenance_scheme', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_maintenance_scheme(self):
        """
        获取保养方案
        :return:
        """
        search = http.request.jsonrequest.get('search', '')
        try:
            domain = [('category', '=', '正常保养')]
            if search:
                domain.append(('name', 'ilike', search))
            scheme_ids = http.request.env["roke.mes.maintenance.scheme"].sudo().search(domain)
            data = []
            for scheme in scheme_ids:
                data.append({
                    'scheme_id': scheme.id,
                    'scheme_name': scheme.name,
                    'item_ids': [
                        {
                            'item_id': item.id,
                            'item_name': item.name or '',
                            'item_note': item.note or ''
                        } for item in scheme.item_ids]
                })
            result = {'state': 'success', 'msgs': '获取成功', 'data': data}
        except Exception as e:
            result = {'state': 'error', 'msgs': e}
        return result

    # 提交设备保养任务
    @http.route('/roke/save_maintenance_order', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False,
                cors='*')
    def save_maintenance_order(self):
        """
        提交保养任务
        :return:
        """
        equipment_id = http.request.jsonrequest.get('equipment_id', False)
        e_location = http.request.jsonrequest.get('e_location', '')
        scheme_id = http.request.jsonrequest.get('scheme_id', False)
        priority = http.request.jsonrequest.get('priority', 'normal')
        normal_state = http.request.jsonrequest.get('normal_state', 'normal')
        user_id = http.request.jsonrequest.get('user_id', False)
        deadline = http.request.jsonrequest.get('deadline', '')
        item_ids = http.request.jsonrequest.get('item_ids', [])
        try:
            # 初始化数据
            if not equipment_id:
                raise UserError('设备禁止为空！')
            if not scheme_id:
                raise UserError('保养方案禁止为空！')
            if not deadline:
                deadline = fields.Date.today()
            if not user_id:
                user_id = http.request.env.user.id
            if priority not in ('low', 'normal', 'high', 'urgent'):
                raise UserError('紧急程度有误！')
            if normal_state not in ('normal', 'abnormal'):
                raise UserError('是否正常有误！')
            # 数据处理
            maintenance_order = http.request.env["roke.mes.maintenance.order"].create({
                'equipment_id': int(equipment_id),
                'e_location': e_location,
                'maintenance_scheme_id': int(scheme_id),
                'priority': priority,
                'normal_state': normal_state,
                'user_id': [user_id],
                'deadline': deadline,
                'type': 'maintain',
                'state': 'assign',
                'item_ids': [
                    (0, 0, {
                        'item_id': item.get('item_id', False),
                        'state': item.get('state', 'wait'),
                        'description': item.get('description', ''),
                        'execute_user_id': item.get('execute_user_id', False) or http.request.env.user.id,
                        'execute_time': fields.Datetime.now()
                    }) for item in item_ids
                ],
            })
            result = {'state': 'success', 'msgs': '提交成功', 'order_id': maintenance_order.id}
        except Exception as e:
            result = {'state': 'error', 'msgs': e}
        return result

    @http.route('/roke/wylj/update_equipment', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False,
                cors='*')
    def update_equipment(self):
        """
        设备维修单结束委外
        """
        order_id = http.request.jsonrequest.get("order_id", False)
        try:
            order_ids = http.request.env["roke.mes.maintenance.order"].sudo().search([('id', '=', order_id)])
            if not order_ids:
                raise UserError('任务不存在！')
            order_ids.write({"state": "finish"})
            order_ids.equipment_id.write({"e_state": "闲置"})
            result = {'state': 'success', 'msgs': '闲置成功'}
        except Exception as e:
            result = {'state': 'error', 'msgs': e}
        return result


class Login(mes_login.Login):

    @http.route('/roke/mes/client_login', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False, cors='*')
    def roke_mes_login(self):
        phone = http.request.jsonrequest.get("phone") or ""
        login = http.request.jsonrequest.get("login") or ""
        if phone and not login:
            phone_user = http.request.env(user=SUPERUSER_ID)['res.users'].search([("login", "=", phone)])
            if not phone_user:
                return {"state": "error", "msgs": "用户不存在"}
        res = super(Login, self).roke_mes_login()
        user_id = http.request.env["res.users"].sudo().browse(res.get("user_id"))
        if user_id.has_group('roke_mes_wylj.group_equipment_leader'):
            res["is_equipment_leader"] = True
        else:
            res["is_equipment_leader"] = False
        if user_id.has_group('roke_mes_wylj.group_equipment_user') or user_id.has_group(
                'roke_mes_wylj.group_equipment_dianjian'):
            res["is_equipment_user"] = True
        else:
            res["is_equipment_user"] = False
        if user_id.has_group('roke_mes_wylj.group_equipment_normal'):
            res["is_equipment_normal"] = True
        else:
            res["is_equipment_normal"] = False
        return res


class Main(http.Controller):
    # 获取基础数据列表
    @http.route('/roke/get_base_list', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_base_list(self):
        """
        获取基础数据列表
        :return:
        """
        search = http.request.jsonrequest.get('search', '')
        model_index = http.request.jsonrequest.get('model_index', '')
        code = http.request.jsonrequest.get('code', '')
        type = http.request.jsonrequest.get('type', '')
        try:
            # 初始化数据
            if not model_index:
                raise UserError('标识禁止为空')
            base_obj = http.request.env[model_index]
            if code:
                base_id = base_obj.search([('code', '=', code)], limit=1)
                if not base_id:
                    raise UserError('编号【%s】对应数据不存在！' % code)
                if model_index == 'roke.mes.equipment':
                    if base_id.mold_id:
                        raise UserError('设备【%s】已绑定模具【%s】，请检查！' % (base_id.name, base_id.mold_id.name))
                base_list = [{
                    'base_id': base.id,
                    'base_name': base.display_name,
                    'base_code': base.code
                } for base in base_id]
                result = {'state': 'success', 'msgs': '获取成功', 'base_list': base_list,
                          "page_no": 1, "total_page": 1, "total_number": 1}
            else:
                # 过滤筛选
                domain = []
                if model_index == 'roke.mes.stock.location':
                    domain.append(('name', 'ilike', search))
                    domain.append(('location_type', 'in', ['内部位置', '中转位置']))
                    employee_id = http.request.env['roke.employee'].search([('user_id', '=', http.request.env.user.id)],
                                                                           limit=1)
                    if employee_id:
                        if employee_id.department_id.location_ids:
                            domain.append(('id', 'in', employee_id.department_id.location_ids.ids))
                elif model_index == 'roke.work.center' and type:
                    domain.append(('name', 'ilike', type))
                elif model_index == 'roke.product':
                    domain.append(('specification', 'ilike', search))
                elif search:
                    domain.append(('name', 'ilike', search))
                base_list = base_obj.search(domain)
                # 处理分页
                page_no = http.request.jsonrequest.get('page_no', 1)  # 页码
                page_size = http.request.jsonrequest.get('page_size', 10)  # 每页记录数
                total_number = len(base_list)
                if page_size:
                    total_page = math.ceil(len(base_list) / page_size)  # 总页数
                    base_list = base_list[(page_no - 1) * page_size: page_no * page_size]  # 当前页记录
                else:
                    total_page = 0
                # 存储
                data = []
                for base in base_list:
                    data.append({
                        'base_id': base.id,
                        'base_name': base.display_name or '',
                        'base_code': base.code
                    })
                result = {'state': 'success', 'msgs': '获取成功', 'base_list': data,
                          "page_no": page_no, "total_page": total_page, "total_number": total_number}
        except Exception as e:
            result = {'state': 'error', 'msgs': e}
        return result

    @http.route('/roke/wylj/get_equipment_upper_die', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_equipment(self):
        """获取设备上模使用查询接口无点检方案校验"""
        code = http.request.jsonrequest.get('code', False)
        role = get_user_role(http.request.env.user.id)
        if not role in ['设备维修人员', '设备组长', '设备一线员工', '设备点检']:
            return {"state": "error",
                    "msgs": "当前用户不在权限组['设备维修人员', '设备组长', '设备一线员工', '设备点检']当中"}
        equipment_obj = http.request.env["roke.mes.equipment"]

        equipment = equipment_obj.search([('qr_code', '=', code)])
        if not equipment:
            _logger.error(f"设备不存在,code:{code}")
            return {"state": "error", "msgs": "设备不存在"}
        _plan_dict = {
            "id": equipment.id,
            "code": equipment.code,
            "name": equipment.name,
            "spot_plan_name": equipment.spot_plan_ids.name,
            "spot_plan_id": equipment.spot_plan_ids.id,
            "spot_plan_code": equipment.spot_plan_ids.code,
            "line_list": []
        }
        line_list = []
        for line in equipment.spot_plan_ids.item_ids:
            value_type = get_value_type_name(line.item_id.input_type)
            # 选择项
            select_item = []
            for item in line.item_id.select_item_ids:
                select_item.append({
                    "value": item.value
                })
            # 选择项
            line_list.append({
                "id": line.item_id.id,
                "name": line.item_id.name,
                "description": line.method or "",
                "value_type": value_type,
                "standard_value": line.standard or "",
                "select_item": select_item
            })
        _plan_dict.update({"line_list": line_list})
        return {"state": "success", "msgs": "查询成功", "records": _plan_dict}

    @http.route('/roke/wylj/get_user_mail_message', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_user_mail_message(self):

        try:
            user_obj = http.request.env['res.users'].search([('id', '=', http.request.uid)])
            notification = http.request.env['mail.notification']

            mails = notification.search(
                [('res_partner_id', '=', user_obj.partner_id.id), ('is_read', '=', False), ('is_remind', '=', False)])
            is_remind = False
            if len(mails) >= 1:
                is_remind = True
                mails.write({"is_remind": True})

            return {"state": "success", "msgs": "查询成功", "is_remind": is_remind}

        except Exception as e:
            return {"state": "error", "msgs": f"出现错误，详细错误入下：{e}"}

    @http.route('/roke/wylj/get_work_order_line_list', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def get_work_order_line_list(self):

        try:
            _self = http.request
            search_values = _self.jsonrequest.get('search_values', False)
            page_no = _self.jsonrequest.get('page_no', 1)
            page_size = _self.jsonrequest.get('page_size', 10)
            # team_id = _self.env.user.employee_id.team_id.id
            domain = []
            # 数据判断
            # 逻辑：
            # 当前   登录用户   绑定员工   所在   班组的工单列表数据，进入详情的单据是计划开始时间最小且是未完工的那个
            # 列表排序：计划开始时间升序，未完工在先
            # 如果：登录用户没有绑定员工（如admin），则获取所有数据
            # 如果：登录用户绑定员工，但是没有绑定班组，获取数据为空，详情提示请绑定班组
            # 如果：工单没有绑定班组，那么不获取这个数据，只获取绑定班组的数据
            employee_id = _self.env['roke.employee'].sudo().search([('user_id', '=', http.request.env.user.id)])
            if not employee_id:
                domain = []
            elif employee_id.team_id:
                domain = [('team_id', '=', employee_id.team_id.id)]
            elif not employee_id.team_id:
                domain = [('id', '<', 0)]  # 空数据
            # if team_id:
            #     domain = [('team_id', '=', team_id)]
            if search_values:
                domain.append("|")
                domain.append("|")
                domain.append(('code', 'ilike', search_values))
                domain.append(('product_id.name', 'ilike', search_values))
                domain.append(('product_id.code', 'ilike', search_values))

            work_order_obj = _self.env['roke.work.order']
            work_order_ids = work_order_obj.search(domain, order="plan_date, state")
            total_number = len(work_order_ids)
            if page_size:
                total_page = math.ceil(len(work_order_ids) / page_size)  # 总页数
                work_order_ids = work_order_ids[(page_no - 1) * page_size: page_no * page_size]  # 当前页记录
            else:
                total_page = 0
            data = []
            for work_order_line in work_order_ids:
                container = _self.env['roke.container.record'].search(
                    [('id', 'in', work_order_line.container_record_ids.ids)], limit=1, order="id desc")
                employee_id = http.request.env['roke.employee'].sudo().search(
                    [('user_id', '=', http.request.env.user.id)], limit=1)
                data.append({
                    'work_order_id': work_order_line.id,  # 工单id
                    'work_order_code': work_order_line.code,  # 工单编号
                    'product_id': work_order_line.product_id.id,  # 产品id
                    'product_name': work_order_line.product_id.name,  # 产品名称
                    'plan_qty': work_order_line.plan_qty,  # 计划数量
                    'finish_qty': work_order_line.finish_qty,  # 完成数量
                    'process_id': work_order_line.process_id.id,  # 工序id
                    'process_name': work_order_line.process_id.name,  # 工序名称
                    'team_id': work_order_line.team_id.id,  # 班组id
                    'team_name': work_order_line.team_id.name,  # 班组名称
                    'equipment_id': work_order_line.equipment_id.id,  # 设备id
                    'equipment_name': work_order_line.equipment_id.name,  # 设备名称
                    'equipment_code': work_order_line.equipment_id.code or '',
                    'mold_id': work_order_line.mold_id.id,  # 模具id
                    'mold_name': work_order_line.mold_id.name,  # 模具名称
                    'mold_code': work_order_line.mold_id.code or '',
                    'tool_id': work_order_line.tool_id.id,  # 工装id
                    'tool_name': work_order_line.tool_id.name,  # 工装名称
                    'tool_code': work_order_line.tool_id.code or '',
                    'state': work_order_line.state or "",  # 工单状态
                    'e_state': ",".join(filter(None, [
                        work_order_line.equipment_id.e_state or "",
                        work_order_line.mold_id.state or "",
                        work_order_line.tool_id.state or ""
                    ])) or "",  # 状态(设备状态,模具状态,工装状态)
                    'lot_code': work_order_line.task_id.lot_code or "",  # 工单状态
                    'class_id': work_order_line.task_id.classes_id.id,
                    'class_name': work_order_line.task_id.classes_id.name or '',
                    'container_code': container.code if container else '',
                    'container_finish_qty': container.finish_qty if container else '',
                    'employee_id': work_order_line.employee_id.id if work_order_line.state == "已完工" else employee_id.id,
                    # 作业人员id
                    'employee_name': work_order_line.employee_id.name if work_order_line.state == "已完工" else employee_id.name,
                    # 作业人员名称
                    'start_time': work_order_line.start_time,  # 开工时间
                    'total_working_hours': work_order_line.total_working_hours,  # 加工时
                    'processing_duration': work_order_line.processing_duration,  # 总加工时长
                    'unqualified_qty': work_order_line.unqualified_qty,  # 不合格数量
                    'task_code': work_order_line.task_id.code,
                })
            return {"state": "success", "msgs": "查询成功", "records": data,
                    "page_no": page_no, "total_number": total_number, "total_page": total_page}

        except Exception as e:
            return {"state": "error", "msgs": f"出现错误，详细错误入下：{e}"}

    @http.route('/roke/wylj/create_work_record', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def create_work_record(self):
        try:
            _self = http.request
            work_order_code = _self.jsonrequest.get('work_order_code', False)  # 报工数量
            finish_qty = _self.jsonrequest.get('finish_qty', False)  # 报工数量
            unqualified_qty = _self.jsonrequest.get('unqualified_qty', False)  # 不合格数量
            start_time = _self.jsonrequest.get('start_time', False)  # 开工时间
            scrap_qty = _self.jsonrequest.get('scrap_qty', False)  # 报废数量
            repair_qty = _self.jsonrequest.get('repair_qty', False)  # 返修数量
            work_hours = _self.jsonrequest.get('work_hours', False)  # 工时
            employee_id = _self.jsonrequest.get('employee_id', False)  # 作业人员

            if not work_order_code:
                return {"state": "error", "msgs": "缺少入参工单编号，请确认编号是否传输！"}
            if not finish_qty:
                return {"state": "error", "msgs": "缺少入参报工数量，请确认数量是否传输！"}
            if not employee_id:
                return {"state": "error", "msgs": "缺少入参作业人员，请确认人员是否传输！"}
            work_order_obj = _self.env['roke.work.order']
            work_order = work_order_obj.search([('code', '=', work_order_code), ("state", "=", "未完工")])

            if not work_order:
                return {"state": "error", "msgs": "工单不存在或已完工，请确认！"}
            # classes = work_order.env["roke.classes"].get_now_classes()
            # res = _self.env["roke.create.work.record.wizard"].create({
            #     "work_order_id": work_order.id,
            #     "plan_qty": work_order.plan_qty,
            #     "employee_id": employee_id,
            #     "team_id": work_order.team_id.id,
            #     "work_center_id": work_order.work_center_id.id,
            #     "classes_id": classes.id,
            #     "finish_qty": finish_qty,
            #     "work_hours": work_hours,
            #     "multi": False,
            #     "unqualified_qty": unqualified_qty,
            #     "scrap_qty": scrap_qty,
            #     "repair_qty": repair_qty,
            # })
            # res.confirm()

            work_order.write({
                "state": "已完工",
                "employee_id": employee_id,
                "unqualified_qty": unqualified_qty,
                "total_working_hours": work_hours,
                "finish_qty": finish_qty,
                "start_time": start_time,
            })
            return {"state": "success", "msgs": "报工成功"}

        except Exception as e:
            return {"state": "error", "msgs": f"出现错误，详细错误入下：{e}"}

    @http.route('/roke/wylj/get_standard_item', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def get_standard_item(self):
        try:
            _self = http.request
            work_order_code = _self.jsonrequest.get('work_order_code', False)  # 工单编号
            work_order_id = _self.jsonrequest.get('work_order_id', False)  # 工单id

            if not work_order_code:
                return {"state": "error", "msgs": "缺少入参工单编号，请确认编号是否传输！"}
            work_order_obj = _self.env['roke.work.order']
            work_order = work_order_obj.search([('code', '=', work_order_code)])
            if not work_order:
                return {"state": "error", "msgs": "工单不存在，请确认！"}
            record = []
            for item in work_order.standard_item_ids:
                record.append({
                    "title": item.title or "",  # 标题
                    "name": item.name or "",  # 内容
                    "image": item.image_1920 or "",  # 图片
                })
            return {"state": "success", "msgs": "查询成功", "records": record}

        except Exception as e:
            return {"state": "error", "msgs": f"出现错误，详细错误入下：{e}"}

    @http.route('/roke/wylj/get_employee', type='json', auth='user', csrf=False, cors="*")
    def wylj_get_employee(self):
        try:
            _self = http.request
            search_value = _self.jsonrequest.get("search_value")
            page_size = _self.jsonrequest.get("page_size") or 20
            page_no = _self.jsonrequest.get("page_no") or 1
            employee_obj = _self.env["roke.employee"]
            domain = []
            if search_value:
                domain.append("|")
                domain.append(("code", "ilike", search_value))
                domain.append(("name", "ilike", search_value))
            employee = employee_obj.search(domain)
            record_total = len(employee)
            total_pages = math.ceil(record_total / page_size)
            employee = employee[(page_no - 1) * page_size: page_no * page_size]
            employee_list = []
            for v in employee:
                employee_list.append({
                    "employee_id": v.id or "",
                    "employee_name": v.name or ""
                })
            return {"code": "success", "message": "获取成功", "data": employee_list, "total_page": total_pages,
                    "page_size": page_size, "page_no": page_no}
        except Exception as e:
            return {"code": "error", "message": str(e)}

    @http.route('/roke/wylj/update_container_record', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def update_container_record(self):
        # 修改容器记录
        try:
            _self = http.request
            work_order_code = _self.jsonrequest.get('work_order_code', False)  # 工单编号
            work_order_id = _self.jsonrequest.get('work_order_id', False)  # 工单id
            container_code = _self.jsonrequest.get('container_code', False)  # 容器编号

            if not work_order_code:
                return {"state": "error", "msgs": "缺少入参工单编号，请确认编号是否传输！"}
            if not container_code:
                return {"state": "error", "msgs": "缺少入参容器编号，请确认编号是否传输！"}
            work_order_obj = _self.env['roke.work.order']
            container_obj = _self.env['roke.mes.stock.location']
            work_order = work_order_obj.search([('code', '=', work_order_code)])
            container = container_obj.search([('container_code', '=', container_code)])
            if not container:
                return {"state": "error", "msgs": "容器不存在，请确认！"}
            if not work_order:
                return {"state": "error", "msgs": "工单不存在，请确认！"}

            container_obj = _self.env['roke.container.record']
            if container_obj.search([("code", "=", container_code), ("work_order_id", "!=", work_order.id)]):
                return {"state": "error", "msgs": "该容器已绑定其他工单，请确认！"}
            container_obj.create({
                "work_order_id": work_order.id,
                "code": container_code,
                "task_id": work_order.task_id.id,
            })

            return {"state": "success", "msgs": "更换成功", "container_code": container_code}

        except Exception as e:
            return {"state": "error", "msgs": f"出现错误，详细错误入下：{e}"}

    @http.route('/roke/wylj/get_task_check_item', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def get_task_check_item(self):
        # 设备点检记录数据查询接口
        try:
            _self = http.request
            equipment_id = _self.jsonrequest.get('equipment_id', False)  # 设备

            if not equipment_id:
                return {"state": "error", "msgs": "缺少入参设备id，请确认设备id是否传输！"}

            equipment_id = _self.env['roke.mes.equipment'].browse(int(equipment_id))  # 设备
            inspection_plan_obj = _self.env['roke.data.collection.inspection.plan']  # 设备数采点检方案
            standard_obj = _self.env['roke.data.collection.standard']  # 数采点检标准
            inspection_plan = inspection_plan_obj.search(
                [('equipment_id', '=', equipment_id.id), ('active', '=', True)], limit=1)
            if not inspection_plan:
                return {"state": "error", "msgs": "设备未绑定数采点检方案"}
            line_ids = []
            for line in inspection_plan.line_ids:
                standard = standard_obj.search([('check_item', '=', line.check_item.item_id)], limit=1)
                current_value = self.get_item_current_value(line.check_item.item_id, equipment_id)
                if standard:

                    line_ids.append({
                        "check_item_id": line.check_item.id or "",  # 点检项id
                        "check_item_name": line.check_item.item_name or "",  # 点检项名称
                        "reference_value": str(standard.lower_limit) + "--" + str(standard.upper_limit),
                        "current_value": current_value,
                        "check_result": "合格" if current_value > standard.lower_limit and current_value < standard.upper_limit else "不合格",
                    })
                else:
                    line_ids.append({
                        "check_item_id": line.check_item.id or "",  # 点检项id
                        "check_item_name": line.check_item.item_name or "",  # 点检项名称
                        "reference_value": "",
                        "current_value": current_value,
                        "check_result": ""
                    })
            return {"state": "success", "msgs": "创建成功", "data": {
                "inspection_plan_id": inspection_plan.id,
                "inspection_plan_name": inspection_plan.name,
                "line_ids": line_ids,
            }}

        except Exception as e:
            return {"state": "error", "msgs": f"出现错误，详细错误入下：{e}"}

    def get_item_current_value(self, item_id, equipment_id):
        # 获取点检项当前值
        base_url = http.request.env(user=SUPERUSER_ID)['ir.config_parameter'].get_param('web.base.url')
        if '************' in base_url:
            res = requests.post(url="http://************:83/go/api/getdata",
                                params={"Apikey": equipment_id.apikey, "Function": 'realtime'}, json=[item_id],
                                timeout=10)
            response_data = res.json()
            # 从响应数据中获取return部分
            return_data = response_data.get("return", {}).get("values", [])
            if return_data:
                return return_data[0]
            else:
                return 0
        else:
            return '暂无'

    @http.route('/roke/wylj/create_task_check_item', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def create_task_check_item(self):
        # 设备点检记录提交接口
        try:
            _self = http.request
            work_order_code = _self.jsonrequest.get('work_order_code', False)  # 工单编号
            work_order_id = _self.jsonrequest.get('work_order_id', False)  # 工单id
            equipment_id = _self.jsonrequest.get('equipment_id', False)  # 设备
            line_ids = _self.jsonrequest.get('line_ids', False)  # 点检项
            employee_id = _self.jsonrequest.get('employee_id', False)  # 作业人员
            inspection_plan_id = _self.jsonrequest.get('inspection_plan_id', False)  # 点检方案id

            if not work_order_code:
                return {"state": "error", "msgs": "缺少入参工单编号，请确认编号是否传输！"}
            if not equipment_id:
                return {"state": "error", "msgs": "缺少入参设备id，请确认设备id是否传输！"}
            if not line_ids:
                return {"state": "error", "msgs": "缺少入参点检项，请确认点检项是否传输！"}
            if not employee_id:
                return {"state": "error", "msgs": "缺少入参作业人员，请确认人员是否传输！"}
            work_order_obj = _self.env['roke.work.order']
            work_order = work_order_obj.search([('code', '=', work_order_code)])
            if not work_order:
                return {"state": "error", "msgs": "工单不存在，请确认！"}

            check_item_obj = _self.env['roke.task.eqpt.check.item']
            check_item = check_item_obj.create({
                "task_id": work_order.task_id.id,
                "equipment_id": equipment_id,
                "inspection_plan_id": inspection_plan_id,
                "employee_id": employee_id,
                "line_ids": [(0, 0, {
                    "check_item": line.get("check_item_id"),
                    "current_value": line.get("current_value"),
                    "reference_value": line.get("reference_value"),
                    "check_result": line.get("check_result"),
                }) for line in line_ids]  # 使用列表推导式替代生成器
            })

            return {"state": "success", "msgs": "创建成功"}

        except Exception as e:
            return {"state": "error", "msgs": f"出现错误，详细错误入下：{e}"}

    @http.route('/roke/wylj/create_self_check_order', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def create_self_check_order(self):
        # 创建自检单
        try:
            _self = http.request
            work_order_code = _self.jsonrequest.get('work_order_code', False)  # 工单编号
            work_order_id = _self.jsonrequest.get('work_order_id', False)  # 工单id
            product_id = _self.jsonrequest.get('product_id', False)  # 产品信息
            employee_id = _self.jsonrequest.get('employee_id', False)  # 自检人
            check_qty = _self.jsonrequest.get('check_qty', False)  # 自检数量
            qualified_qty = _self.jsonrequest.get('qualified_qty', False)  # 合格数量

            if not work_order_code:
                return {"state": "error", "msgs": "缺少入参工单编号，请确认编号是否传输！"}
            if not product_id:
                return {"state": "error", "msgs": "缺少入参产品id，请确认产品id是否传输！"}
            if not qualified_qty:
                return {"state": "error", "msgs": "缺少入参合格数量，请确认合格数量是否传输！"}
            if not check_qty:
                return {"state": "error", "msgs": "缺少入参自检数量，请确认自检数量是否传输！"}
            if not employee_id:
                return {"state": "error", "msgs": "缺少入参自检人，请确认自检人是否传输！"}
            work_order_obj = _self.env['roke.work.order']
            work_order = work_order_obj.search([('code', '=', work_order_code)])
            if not work_order:
                return {"state": "error", "msgs": "工单不存在，请确认！"}
            if not work_order.container_record_ids:
                return {"state": "error", "msgs": "未绑定容器，不可自检！"}
            check_order_obj = _self.env["roke.self.check.order"]
            check_order = check_order_obj.create({
                "task_id": work_order.task_id.id,
                "work_order_id": work_order.id,
                "employee_id": employee_id,
                "check_qty": check_qty,
                "qualified_qty": qualified_qty,
                "product_id": product_id,
            })
            work_order.write({'state': '自检完成'})

            return {"state": "success", "msgs": "创建成功", "check_order": check_order.id}

        except Exception as e:
            return {"state": "error", "msgs": f"出现错误，详细错误入下：{e}"}

    # 创建报修维修单
    @http.route('/roke/wylj/create_maintenance_order', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def create_maintenance_order(self):
        try:
            _self = http.request
            equipment_id = _self.jsonrequest.get('equipment_id', False)  # 设备id
            mold_id = _self.jsonrequest.get('mold_id', False)  # 模具id
            tool_id = _self.jsonrequest.get('tool_id', False)  # 工装id
            is_equipment_maintenance = _self.jsonrequest.get('is_equipment_maintenance', False)  # 是否设备维修
            is_mold_maintenance = _self.jsonrequest.get('is_mold_maintenance', False)  # 是否模具维修
            is_tool_maintenance = _self.jsonrequest.get('is_tool_maintenance', False)  # 是否工装维修
            equipment_failure_id = _self.jsonrequest.get('failure_id', False)  # 设备故障类型
            equipment_failure_note = _self.jsonrequest.get('failure_note', False)  # 设备故障描述
            tool_failure_id = _self.jsonrequest.get('tool_failure_id', False)  # 工装故障类型
            tool_failure_note = _self.jsonrequest.get('tool_failure_note', False)  # 工装故障描述
            mold_failure_id = _self.jsonrequest.get('mold_failure_id', False)  # 模具故障类型
            mold_failure_note = _self.jsonrequest.get('mold_failure_note', False)  # 模具故障描述

            equipment_obj = _self.env["roke.mes.maintenance.order"]  # 设备维维修任务对象
            tool_obj = _self.env["roke.tool.repair.record"]  # 工装维维修任务对象
            mold_obj = _self.env["roke.mold.repair.records"]  # 模具维维修任务对象

            if is_equipment_maintenance:
                if not equipment_id:
                    return {"state": "error", "msgs": "缺少入参设备id，请确认设备id是否传输！"}
                if not equipment_failure_id:
                    return {"state": "error", "msgs": "缺少入参设备故障类型，请确认设备故障类型是否传输！"}
                equipment = _self.env["roke.mes.equipment"].browse(equipment_id)
                order_equipment = equipment_obj.create({
                    "equipment_id": int(equipment.id),
                    # "type": "repair",
                    "report_time": datetime.datetime.now(),
                    "failure_id": int(equipment_failure_id),
                    "fault_description": equipment_failure_note,
                    "state": "wait",
                })
                equipment.write({"in_repair": True, "e_state": "报修"})
            if is_tool_maintenance:
                if not tool_id:
                    return {"state": "error", "msgs": "缺少入参工装id，请确认工装id是否传输！"}
                if not tool_failure_id:
                    return {"state": "error", "msgs": "缺少入参工装故障类型，请确认工装故障类型是否传输！"}
                tool = _self.env["roke.mes.tool"].browse(tool_id)
                order_tool = tool_obj.create({
                    "tool_id": tool.id,
                    "failure_id": tool_failure_id,
                    "note": tool_failure_note,
                    "start_repair_time": datetime.datetime.now(),
                    "state": "草稿",
                })
                tool.write({"state": "维修"})
            if is_mold_maintenance:
                if not mold_id:
                    return {"state": "error", "msgs": "缺少入参模具id，请确认模具id是否传输！"}
                if not mold_failure_id:
                    return {"state": "error", "msgs": "缺少入参模具故障类型，请确认模具故障类型是否传输！"}
                mold = _self.env["roke.mold.ledger"].browse(mold_id)
                order_mold = mold_obj.create({
                    "mold_id": mold.id,
                    "failure_id": mold_failure_id,
                    "note": mold_failure_note,
                    "start_repair_time": datetime.datetime.now(),
                    "state": "草稿",
                })
                mold.write({"state": "维修"})
            if not is_tool_maintenance and not is_mold_maintenance and not is_equipment_maintenance:
                return {"state": "error", "msgs": "请选择维修！"}

            return {"state": "success", "msgs": "创建成功"}

        except Exception as e:
            return {"state": "error", "msgs": f"出现错误，详细错误入下：{e}"}

    # 获取
    @http.route('/roke/wylj/get_failure', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def get_failure(self):
        try:
            _self = http.request
            search_value = _self.jsonrequest.get("search_value")
            page_size = _self.jsonrequest.get("page_size") or 20
            page_no = _self.jsonrequest.get("page_no") or 1
            type = _self.jsonrequest.get("type") or ""
            if type == "设备":
                failure_obj = _self.env["equipment.failure"]
            elif type == "工装":
                failure_obj = _self.env["tool.failure"]
            elif type == "模具":
                failure_obj = _self.env["mold.failure"]
            else:
                return {"state": "error", "msgs": "故障必填"}
            domain = []
            if search_value:
                domain.append("|")
                domain.append(("code", "ilike", search_value))
                domain.append(("name", "ilike", search_value))
            failure = failure_obj.search(domain)
            record_total = len(failure)
            total_pages = math.ceil(record_total / page_size)
            failure = failure[(page_no - 1) * page_size: page_no * page_size]
            failure_list = []
            for v in failure:
                failure_list.append({
                    "failure_id": v.id or "",
                    "failure_code": v.code or "",
                    "failure_name": v.name or ""
                })
            return {"code": "success", "message": "获取成功", "data": failure_list, "total_page": total_pages,
                    "page_size": page_size, "page_no": page_no}
        except Exception as e:
            return {"code": "error", "message": str(e)}

    @http.route('/roke/wylj/create_scrap_order', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def create_scrap_order(self):
        container_code = http.request.jsonrequest.get('container_code', '')
        scrap_qty = http.request.jsonrequest.get('scrap_qty', 0)
        reason_id = http.request.jsonrequest.get('reason_id', False)
        try:
            # 初始化
            if not container_code:
                raise UserError('容器编号为空')
            if not scrap_qty:
                raise UserError('报废数量为空')
            if not reason_id:
                raise UserError('报废原因为空')
            record_id = http.request.env['roke.work.record'].sudo().search([('container_code', '=', container_code)],
                                                                           limit=1)
            if not record_id:
                raise UserError('该容器编号还未报工！')
            http.request.env['roke.scrap.order'].sudo().create({
                'wr_id': record_id.id,
                'container_code': container_code,
                'total': float(scrap_qty),
                'clean_line_ids': [(0, 0, {
                    'reason_id': int(reason_id),
                    'qty': float(scrap_qty)
                })]
            })
            result = {'state': 'success', 'msgs': '处理成功'}
        except Exception as e:
            result = {'state': 'error', 'msgs': e}
        return result

    @http.route('/roke/wylj/get_scrap_reason_list', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_scrap_reason_list(self):
        """
        获取报废原因
        :return:
        """
        search = http.request.jsonrequest.get('search', '')
        try:
            domain = []
            if search:
                domain = [("name", "ilike", search)]
            scrap_reason_list = http.request.env['roke.scrap.reason'].search(domain)
            # 处理分页
            page_no = http.request.jsonrequest.get('page_no', 1)  # 页码
            page_size = http.request.jsonrequest.get('page_size', 10)  # 每页记录数
            total_number = len(scrap_reason_list)
            if page_size:
                total_page = math.ceil(len(scrap_reason_list) / page_size)  # 总页数
                scrap_reason_list = scrap_reason_list[(page_no - 1) * page_size: page_no * page_size]  # 当前页记录
            else:
                total_page = 0
            result = []
            for scrap_reason in scrap_reason_list:
                result.append({
                    "id": scrap_reason.id,
                    "name": scrap_reason.display_name
                })
            result = {'state': 'success', 'msgs': '获取成功', 'data': result,
                      "page_no": page_no, "total_page": total_page, "total_number": total_number}
        except Exception as e:
            result = {'state': 'error', 'msgs': e}
        return result

    @http.route('/roke/wylj/create_work_record_schedule', type='json', methods=['POST', 'OPTIONS'], auth="none",
                csrf=False,
                cors='*')
    def create_work_record_schedule(self):
        """
        设备产能报工
        :return:
        """
        try:
            _self = http.request
            apikey = _self.jsonrequest.get('apikey', False)
            if not apikey:
                raise UserError('apikey不能为空')
            product_id = _self.jsonrequest.get('product_id', False)
            schedule_obj = _self.env["roke.equipment.production.capacity.schedule"].sudo()
            work_order_obj = _self.env["roke.work.order"].sudo()
            schedule = schedule_obj.search([('equipment_id.apikey', '=', apikey)], limit=1)
            if not schedule:
                raise UserError('该设备绑定的产能表不存在')
            if product_id:
                line_ids = schedule.line_ids.filtered(lambda line: line.product_id.id == product_id)
            else:
                line_ids = schedule.line_ids
            if not line_ids:
                raise UserError('该设备下未配置该产品的产能表')
            for s in line_ids:
                domain = [("equipment_id", "=", schedule.equipment_id.id), ("product_id", "=", s.product_id.id)]
                if s.mold_id:
                    domain.append(("mold_id", "=", s.mold_id.id)),
                if s.tool_id:
                    domain.append(("tool_id", "=", s.tool_id.id)),
                work_order = work_order_obj.search(domain)
                if len(work_order) == 1:
                    if work_order.state == "暂停":
                        raise UserError('工单已暂停，禁止报工！')
                    if work_order.state != "自检完成":
                        raise UserError('工单未自检，禁止报工！')
                    # 判断报工数量是否大于可报工数量
                    production_per_mold = s.production_per_mold
                    if s.production_per_mold + work_order.finish_qty > work_order.plan_qty:
                        production_per_mold = work_order.plan_qty - work_order.finish_qty
                    # 获取班组
                    classes = work_order.env["roke.classes"].sudo().get_now_classes()
                    # 生成报工记录
                    res = _self.env["roke.create.work.record.wizard"].sudo().create({
                        "work_order_id": work_order.id,
                        "plan_qty": work_order.plan_qty,
                        "employee_id": work_order.team_id.manager_id.id,
                        "team_id": work_order.team_id.id,
                        "work_center_id": work_order.work_center_id.id,
                        "classes_id": classes.id,
                        "finish_qty": production_per_mold,
                        "work_hours": 0,
                        "multi": False,
                        "unqualified_qty": 0,
                        "scrap_qty": 0,
                        "repair_qty": 0,
                    })
                    res.confirm()
                else:
                    raise UserError('该设备下的该产品的工单匹配失败，查询到的工单不唯一，请检查工单数量')
            return {'state': 'success', 'msgs': '报工创建成功'}
        except Exception as e:
            return {'state': 'error', 'msgs': e}

    @http.route('/roke/wylj/get_quality_order', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False,
                cors='*')
    def get_quality_order(self):
        """
        获取质检单列表
        根据质检类型区分巡检，到货检，发货检三个类型
        """
        try:
            _self = http.request
            order_type = _self.jsonrequest.get('order_type', False)  # 质检类型必传 巡检，到货检，发货检三个固定值
            page_no = _self.jsonrequest.get('page_no', 1)  # 页码
            page_size = _self.jsonrequest.get('page_size', 10)  # 每页记录数
            search_values = _self.jsonrequest.get('search_values', False)  # 搜索条件
            order = _self.jsonrequest.get('order', "asc")  # 降序不传默认升序，传desc降序，asc升序
            state = _self.jsonrequest.get('state', False)  # 增加状态筛选
            if order_type == "巡检":
                domain = [("quality_type", "=", "巡检")]
            elif order_type == "到货检":
                domain = [("quality_type", "=", "到货检")]
            elif order_type == "发货检":
                domain = [("quality_type", "=", "发货检")]
            else:
                raise UserError('质检类型不能为空')

            if state:
                domain.append(("state", "=", state))

            if search_values:
                domain.append(("code", "ilike", search_values))

            order = "order_date " + order

            # 获取质检单列表
            quality_order_list = _self.env["roke.quality.order"].search(domain, order=order)

            # 处理分页
            total_number = len(quality_order_list)
            if page_size:
                total_page = math.ceil(len(quality_order_list) / page_size)  # 总页数
                quality_order_list = quality_order_list[(page_no - 1) * page_size: page_no * page_size]  # 当前页记录
            else:
                total_page = 0

            # 组装返回数据
            result = []
            for quality_order in quality_order_list:
                result.append({
                    "quality_order_id": quality_order.id,  # 质检单id
                    "code": quality_order.code,  # 质检单编号
                    "order_date": quality_order.order_date,  # 质检单日期
                    "pass_qty": quality_order.pass_qty,  # 合格数量
                    "result": quality_order.result,  # 检验结果
                    "container_id": quality_order.container_id.id,  # 检验容器id
                    "container_name": quality_order.container_id.name,  # 检验容器名称
                    "order_type": quality_order.quality_type,  # 单据类型
                    "scheme_id": quality_order.scheme_id.id,  # 检验方案id
                    "scheme_name": quality_order.scheme_id.name,  # 检验方案名称
                    "fail_qty": quality_order.fail_qty,  # 不合格数
                    "department_id": quality_order.department_id.id,  # 检验部门id
                    "department_name": quality_order.department_id.name,  # 检验部门名称
                    "employee_name": ", ".join(quality_order.employee_ids.mapped("name")),  # 检验人名称(多个用逗号分隔)
                    "product_id": quality_order.product_id.id,  # 检验产品id
                    "product_name": quality_order.product_id.name,  # 检验产品名称
                    "plan_qty": quality_order.plan_qty,  # 报建数量
                    "pass_rate": quality_order.pass_rate,  # 合格率
                    "state": quality_order.state,  # 状态
                    "stock_picking_id": quality_order.stock_picking_id.id,  # 关联的发货单id
                    "stock_picking_code": quality_order.stock_picking_id.code,  # 关联的发货单编号
                    "stock_department_id": quality_order.stock_picking_id.sale_id.department_id.id,  # 发货部门
                    "stock_department_name": quality_order.stock_picking_id.sale_id.department_id.name,  # 发货部门
                    "task_id": quality_order.task_id.id,  # 关联的生产任务id
                    "task_code": quality_order.task_id.code,  # 关联的生产任务编号
                    "workshop_id": quality_order.workshop_id.id,  # 关联的生产车间id
                    "workshop_name": quality_order.workshop_id.name,  # 关联的生产车间名称
                    "purchase_order_id": quality_order.purchase_order_id.id,  # 关联的采购单id
                    "purchase_order_code": quality_order.purchase_order_id.code,  # 关联的采购单编号
                    "purchase_department_id": quality_order.purchase_order_id.purchase_id.department_id.id,  # 采购部门
                    "purchase_department_name": quality_order.purchase_order_id.purchase_id.department_id.name,  # 采购部门
                })
            return {'state': 'success', 'msgs': '获取成功', 'data': result, "page_no": page_no,
                    "total_page": total_page,
                    "total_number": total_number}
        except Exception as e:
            return {'state': 'error', 'msgs': e}

    @http.route('/roke/wylj/get_quality_order_line', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False,
                cors='*')
    def get_quality_order_line(self):
        """
        获取质检单详细信息
        根据质检单id获取详细数据
        """
        try:
            _self = http.request
            quality_order_id = _self.jsonrequest.get('quality_order_id', False)  # 质检单id

            if not quality_order_id:
                raise UserError('质检单id不能为空')
            # 获取质检单列表
            quality_order = _self.env["roke.quality.order"].browse(int(quality_order_id))
            processing_obj = _self.env["roke.quality.processing"]
            processing = processing_obj.search([("quality_order_id", "=", quality_order.id)])
            # 组装返回数据
            data = {
                "quality_order_id": quality_order.id,  # 质检单id
                "code": quality_order.code,  # 质检单编号
                "order_date": quality_order.order_date,  # 质检单日期
                "pass_qty": quality_order.pass_qty,  # 合格数量
                "result": quality_order.result,  # 检验结果
                "container_id": quality_order.container_id.id,  # 检验容器id
                "container_name": quality_order.container_id.name,  # 检验容器名称
                "order_type": quality_order.quality_type,  # 单据类型
                "scheme_id": quality_order.scheme_id.id,  # 检验方案id
                "scheme_name": quality_order.scheme_id.name,  # 检验方案名称
                "fail_qty": quality_order.fail_qty,  # 不合格数
                "department_id": quality_order.department_id.id,  # 检验部门id
                "department_name": quality_order.department_id.name,  # 检验部门名称
                "employee_name": ", ".join(quality_order.employee_ids.mapped("name")),  # 检验人名称(多个用逗号分隔)
                "product_id": quality_order.product_id.id,  # 检验产品id
                "product_name": quality_order.product_id.name,  # 检验产品名称
                "plan_qty": quality_order.plan_qty,  # 报建数量
                "pass_rate": quality_order.pass_rate,  # 合格率
                "stock_picking_id": quality_order.stock_picking_id.id,  # 关联的发货单id
                "stock_picking_code": quality_order.stock_picking_id.code,  # 关联的发货单编号
                "stock_department_id": quality_order.stock_picking_id.sale_id.department_id.id,  # 发货部门
                "stock_department_name": quality_order.stock_picking_id.sale_id.department_id.name,  # 发货部门
                "task_id": quality_order.task_id.id,  # 关联的生产任务id
                "task_code": quality_order.task_id.code,  # 关联的生产任务编号
                "workshop_id": quality_order.workshop_id.id,  # 关联的生产车间id
                "workshop_name": quality_order.workshop_id.name,  # 关联的生产车间名称
                "purchase_order_id": quality_order.purchase_order_id.id,  # 关联的采购单id
                "purchase_order_code": quality_order.purchase_order_id.code,  # 关联的采购单编号
                "purchase_department_id": quality_order.purchase_order_id.purchase_id.department_id.id,  # 采购部门
                "purchase_department_name": quality_order.purchase_order_id.purchase_id.department_id.name,  # 采购部门
                "line_ids": [],  # 质检明细
                "is_processing_number": len(processing),  # 下级质检处理单数量,
            }
            line_ids = []
            for line in quality_order.line_ids:
                line_ids.append({
                    "id": line.id or "",  # 质检明细id
                    "sequence": line.sequence or "",  # 序号
                    "item_id": line.item_id.id or "",  # 质检项目ID
                    "item_name": line.item_id.name or "",  # 质检项目名称
                    "scheme_line_id": line.scheme_line_id.id or "",  # 质检方案明细ID
                    "standard_value": line.standard_value or "",  # 标准值
                    "result": line.result or "",  # 检测值
                    "auto_judge": line.auto_judge or "",  # 自动判定结果
                    "analysis_method": line.analysis_method or "",  # 分析方法
                    "quality_method_id": line.quality_method_id.id or "",  # 检验方法ID
                    "quality_method_name": line.quality_method_id.name or "",  # 检验方法名称
                    "destructive": line.destructive or "",  # 是否破坏性质检
                    "key": line.key or "",  # 是否关键检测
                    "finish_time": line.finish_time or "",  # 完成时间
                    "note": line.note or "",  # 备注
                    "judge_target": line.judge_target or "",  # 判定状态
                    "operator": line.operator or "",  # 比较符
                    "upper_limit": line.upper_limit or "",  # 上限值
                    "upper_limit_tolerance": line.upper_limit_tolerance or "",  # 上限公差
                    "lower_limit": line.lower_limit or "",  # 下限值
                    "lower_limit_tolerance": line.lower_limit_tolerance or "",  # 下限公差
                    "qty": line.qty or "",  # 不合格数量
                    "value_range_ids": [{
                        "id": range.id or "",  # 取值范围ID
                        "min_value": range.min_value or "",  # 最小值
                        "max_value": range.max_value or ""  # 最大值
                    } for range in line.value_range_ids]
                })

            data["line_ids"] = line_ids

            return {'state': 'success', 'msgs': '获取成功', 'data': data}
        except Exception as e:
            return {'state': 'error', 'msgs': e}

    @http.route('/roke/wylj/get_quality_order_line_2', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False,
                cors='*')
    def get_quality_order_line_2(self):
        """
        获取质检单详细信息
        根据质检单id获取详细数据
        """
        try:
            _self = http.request
            quality_order_id = _self.jsonrequest.get('quality_order_id', False)  # 质检单id

            if not quality_order_id:
                raise UserError('质检单id不能为空')
            # 获取质检单列表
            quality_order = _self.env["roke.quality.order"].browse(int(quality_order_id))

            # 组装返回数据
            data = {
                "quality_order_id": quality_order.id,  # 质检单id
                "code": quality_order.code,  # 质检单编号
                "order_date": quality_order.order_date,  # 质检单日期
                "pass_qty": quality_order.pass_qty,  # 合格数量
                "result": quality_order.result,  # 检验结果
                "container_id": quality_order.container_id.id,  # 检验容器id
                "container_name": quality_order.container_id.name,  # 检验容器名称
                "order_type": quality_order.quality_type,  # 单据类型
                "scheme_id": quality_order.scheme_id.id,  # 检验方案id
                "scheme_name": quality_order.scheme_id.name,  # 检验方案名称
                "fail_qty": quality_order.fail_qty,  # 不合格数
                "department_id": quality_order.department_id.id,  # 检验部门id
                "department_name": quality_order.department_id.name,  # 检验部门名称
                "employee_name": ", ".join(quality_order.employee_ids.mapped("name")),  # 检验人名称(多个用逗号分隔)
                "product_id": quality_order.product_id.id,  # 检验产品id
                "product_name": quality_order.product_id.name,  # 检验产品名称
                "plan_qty": quality_order.plan_qty,  # 报建数量
                "pass_rate": quality_order.pass_rate,  # 合格率
                "line_ids": []  # 质检明细
            }
            line_ids = []
            for line in quality_order.line_ids:
                line_ids.append({
                    "id": line.id or "",  # 质检明细id
                    "sequence": line.sequence or "",  # 序号
                    "item_id": line.item_id.id or "",  # 质检项目ID
                    "item_name": line.item_id.name or "",  # 质检项目名称
                    "scheme_line_id": line.scheme_line_id.id or "",  # 质检方案明细ID
                    "standard_value": line.standard_value or "",  # 标准值
                    "result": line.result or "",  # 检测值
                    "auto_judge": line.auto_judge or "",  # 自动判定结果
                    "analysis_method": line.analysis_method or "",  # 分析方法
                    "quality_method_id": line.quality_method_id.id or "",  # 检验方法ID
                    "quality_method_name": line.quality_method_id.name or "",  # 检验方法名称
                    "destructive": line.destructive or "",  # 是否破坏性质检
                    "key": line.key or "",  # 是否关键检测
                    "finish_time": line.finish_time or "",  # 完成时间
                    "note": line.note or "",  # 备注
                    "judge_target": line.judge_target or "",  # 判定状态
                    "operator": line.operator or "",  # 比较符
                    "upper_limit": line.upper_limit or "",  # 上限值
                    "upper_limit_tolerance": line.upper_limit_tolerance or "",  # 上限公差
                    "lower_limit": line.lower_limit or "",  # 下限值
                    "lower_limit_tolerance": line.lower_limit_tolerance or "",  # 下限公差
                    "qty": line.qty or "",  # 不合格数量
                    "value_range_ids": [{
                        "id": range.id or "",  # 取值范围ID
                        "min_value": range.min_value or "",  # 最小值
                        "max_value": range.max_value or ""  # 最大值
                    } for range in line.value_range_ids]
                })

            data["line_ids"] = line_ids

            return {'state': 'success', 'msgs': '获取成功', 'data': data}
        except Exception as e:
            return {'state': 'error', 'msgs': e}

    @http.route('/roke/wylj/get_quality_order_line_3', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False,
                cors='*')
    def get_quality_order_line_3(self):
        """
        获取质检单详细信息
        根据质检单id获取详细数据
        """
        try:
            _self = http.request
            quality_order_id = _self.jsonrequest.get('quality_order_id', False)  # 质检单id

            if not quality_order_id:
                raise UserError('质检单id不能为空')
            # 获取质检单列表
            quality_order = _self.env["roke.quality.order"].browse(int(quality_order_id))

            # 组装返回数据
            data = {
                "quality_order_id": quality_order.id,  # 质检单id
                "code": quality_order.code,  # 质检单编号
                "order_date": quality_order.order_date,  # 质检单日期
                "pass_qty": quality_order.pass_qty,  # 合格数量
                "result": quality_order.result,  # 检验结果
                "container_id": quality_order.container_id.id,  # 检验容器id
                "container_name": quality_order.container_id.name,  # 检验容器名称
                "order_type": quality_order.quality_type,  # 单据类型
                "scheme_id": quality_order.scheme_id.id,  # 检验方案id
                "scheme_name": quality_order.scheme_id.name,  # 检验方案名称
                "fail_qty": quality_order.fail_qty,  # 不合格数
                "department_id": quality_order.department_id.id,  # 检验部门id
                "department_name": quality_order.department_id.name,  # 检验部门名称
                "employee_name": ", ".join(quality_order.employee_ids.mapped("name")),  # 检验人名称(多个用逗号分隔)
                "product_id": quality_order.product_id.id,  # 检验产品id
                "product_name": quality_order.product_id.name,  # 检验产品名称
                "plan_qty": quality_order.plan_qty,  # 报建数量
                "pass_rate": quality_order.pass_rate,  # 合格率
                "line_ids": []  # 质检明细
            }
            line_ids = []
            for line in quality_order.line_ids:
                line_ids.append({
                    "id": line.id or "",  # 质检明细id
                    "sequence": line.sequence or "",  # 序号
                    "item_id": line.item_id.id or "",  # 质检项目ID
                    "item_name": line.item_id.name or "",  # 质检项目名称
                    "scheme_line_id": line.scheme_line_id.id or "",  # 质检方案明细ID
                    "standard_value": line.standard_value or "",  # 标准值
                    "result": line.result or "",  # 检测值
                    "auto_judge": line.auto_judge or "",  # 自动判定结果
                    "analysis_method": line.analysis_method or "",  # 分析方法
                    "quality_method_id": line.quality_method_id.id or "",  # 检验方法ID
                    "quality_method_name": line.quality_method_id.name or "",  # 检验方法名称
                    "destructive": line.destructive or "",  # 是否破坏性质检
                    "key": line.key or "",  # 是否关键检测
                    "finish_time": line.finish_time or "",  # 完成时间
                    "note": line.note or "",  # 备注
                    "judge_target": line.judge_target or "",  # 判定状态
                    "operator": line.operator or "",  # 比较符
                    "upper_limit": line.upper_limit or "",  # 上限值
                    "upper_limit_tolerance": line.upper_limit_tolerance or "",  # 上限公差
                    "lower_limit": line.lower_limit or "",  # 下限值
                    "lower_limit_tolerance": line.lower_limit_tolerance or "",  # 下限公差
                    "qty": line.qty or "",  # 不合格数量
                    "value_range_ids": [{
                        "id": range.id or "",  # 取值范围ID
                        "min_value": range.min_value or "",  # 最小值
                        "max_value": range.max_value or ""  # 最大值
                    } for range in line.value_range_ids]
                })

            data["line_ids"] = line_ids

            return {'state': 'success', 'msgs': '获取成功', 'data': data}
        except Exception as e:
            return {'state': 'error', 'msgs': e}

    @http.route('/roke/wylj/submit_quality_order', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False,
                cors='*')
    def submit_quality_order(self):
        """
        提交质检明细检验结果--巡检
        """
        try:
            _self = http.request
            quality_order_id = _self.jsonrequest.get('quality_order_id', False)  # 质检单id
            line_ids = _self.jsonrequest.get('line_ids', False)  # 质检明细

            if not quality_order_id:
                raise UserError('质检单id不能为空')

            if not line_ids:
                raise UserError('质检明细不能为空')

            # 获取质检单列表
            quality_order = _self.env["roke.quality.order"].browse(int(quality_order_id))
            quality_order_line_obj = _self.env["roke.quality.order.line"]

            for line in line_ids:
                quality_order_line = quality_order_line_obj.browse(int(line.get("id")))
                quality_order_line.write({
                    "result": line.get("result") or "",  # 检测值
                    "auto_judge": line.get("auto_judge") or "",  # 自动判定结果
                    "finish_time": (datetime.datetime.now() - datetime.timedelta(hours=8)).strftime(
                        "%Y-%m-%d %H:%M:%S"),  # 当前时间减8小时
                    "note": line.get("note") or "",  # 备注
                    "qty": line.get("qty") or "",  # 不合格数
                })
            quality_order.make_finish()

            return {'state': 'success', 'msgs': '提交成功', 'data': {
                "result": quality_order.result or "",  # 是否合格
                "fail_qty": quality_order.fail_qty or "",  # 不合格数,
                "pass_qty": quality_order.pass_qty or "",  # 合格数,
                "product_id": quality_order.product_id.id or "",  # 产品id,
                "product_name": quality_order.product_id.name or "",  # 产品名称,
                "task_id": quality_order.task_id.id or "",  # 任务id,
                "task_code": quality_order.task_id.code or "",  # 任务编号,
            }
                    }

        except Exception as e:
            return {'state': 'error', 'msgs': e}

    @http.route('/roke/wylj/submit_quality_order_purchase', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False,
                cors='*')
    def submit_quality_order_purchase(self):
        """
        提交质检明细检验结果 -- 到货检
        """
        try:
            _self = http.request
            quality_order_id = _self.jsonrequest.get('quality_order_id', False)  # 质检单id
            line_ids = _self.jsonrequest.get('line_ids', False)  # 质检明细

            if not quality_order_id:
                raise UserError('质检单id不能为空')

            if not line_ids:
                raise UserError('质检明细不能为空')

            # 获取质检单列表
            quality_order = _self.env["roke.quality.order"].browse(int(quality_order_id))
            quality_order_line_obj = _self.env["roke.quality.order.line"]

            for line in line_ids:
                quality_order_line = quality_order_line_obj.browse(int(line.get("id")))
                quality_order_line.write({
                    "result": line.get("result") or "",  # 检测值
                    "auto_judge": line.get("auto_judge") or "",  # 自动判定结果
                    "finish_time": (datetime.datetime.now() - datetime.timedelta(hours=8)).strftime(
                        "%Y-%m-%d %H:%M:%S"),  # 当前时间减8小时
                    "note": line.get("note") or "",  # 备注
                    "qty": line.get("qty") or "",  # 不合格数
                })
            quality_order.make_finish()

            return {'state': 'success', 'msgs': '提交成功', 'data': {
                "result": quality_order.result or "",  # 是否合格
                "fail_qty": quality_order.fail_qty or "",  # 不合格数,
                "pass_qty": quality_order.pass_qty or "",  # 合格数,
                "product_id": quality_order.product_id.id or "",  # 产品id,
                "product_name": quality_order.product_id.name or "",  # 产品名称,
                "purchase_order_id": quality_order.purchase_order_id.id or "",  # 采购入库单id,
                "purchase_order_code": quality_order.purchase_order_id.code or "",  # 采购入库单编号,
            }
                    }

        except Exception as e:
            return {'state': 'error', 'msgs': e}

    @http.route('/roke/wylj/submit_quality_order_stock_picking', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False,
                cors='*')
    def submit_quality_order_stock_picking(self):
        """
        提交质检明细检验结果 -- 发货检
        """
        try:
            _self = http.request
            quality_order_id = _self.jsonrequest.get('quality_order_id', False)  # 质检单id
            line_ids = _self.jsonrequest.get('line_ids', False)  # 质检明细

            if not quality_order_id:
                raise UserError('质检单id不能为空')

            if not line_ids:
                raise UserError('质检明细不能为空')

            # 获取质检单列表
            quality_order = _self.env["roke.quality.order"].browse(int(quality_order_id))
            quality_order_line_obj = _self.env["roke.quality.order.line"]

            for line in line_ids:
                quality_order_line = quality_order_line_obj.browse(int(line.get("id")))
                quality_order_line.write({
                    "result": line.get("result") or "",  # 检测值
                    "auto_judge": line.get("auto_judge") or "",  # 自动判定结果
                    "finish_time": (datetime.datetime.now() - datetime.timedelta(hours=8)).strftime(
                        "%Y-%m-%d %H:%M:%S"),  # 当前时间减8小时
                    "note": line.get("note") or "",  # 备注
                    "qty": line.get("qty") or "",  # 不合格数
                })
            quality_order.make_finish()

            return {'state': 'success', 'msgs': '提交成功', 'data': {
                "result": quality_order.result or "",  # 是否合格
                "fail_qty": quality_order.fail_qty or "",  # 不合格数,
                "pass_qty": quality_order.pass_qty or "",  # 合格数,
                "product_id": quality_order.product_id.id or "",  # 产品id,
                "product_name": quality_order.product_id.name or "",  # 产品名称,
                "stock_picking_id": quality_order.stock_picking_id.id or "",  # 发货单id,
                "stock_picking_code": quality_order.stock_picking_id.code or "",  # 发货单编号,
            }
                    }

        except Exception as e:
            return {'state': 'error', 'msgs': e}

    @http.route('/roke/wylj/get_quality_processing_sorting', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def get_quality_processing_sorting(self):
        """
        获取质检处理单列表  -- 分选单
        """
        try:
            _self = http.request
            # processing_type = _self.jsonrequest.get('processing_type', False)
            quality_order_id = _self.jsonrequest.get('quality_order_id', False)
            search_value = _self.jsonrequest.get('search_value', False)
            page_no = _self.jsonrequest.get('page_no', 1)
            page_size = _self.jsonrequest.get('page_size', 10)

            # domain = []
            # if processing_type:
            #     domain.append(('processing_type', '=', processing_type))
            domain = []

            if quality_order_id:
                domain.append(('quality_order_id', '=', int(quality_order_id)))
            if search_value:
                domain.append(('code', 'ilike', search_value))

            # domain.append(('processing_type', '=', "分选单"))

            processings = _self.env['roke.quality.processing'].search(domain)

            # 分页处理
            total = len(processings)
            total_page = math.ceil(total / page_size) if page_size else 0
            if page_size:
                processings = processings[(page_no - 1) * page_size: page_no * page_size]

            result = []
            for processing in processings:
                result.append({
                    'id': processing.id,  # 质检处理单id
                    'code': processing.code,  # 质检处理单编号
                    'processing_type': processing.processing_type,  # 类型
                    'quality_order_id': processing.quality_order_id.id,  # 质检单id
                    'quality_order_code': processing.quality_order_id.code,  # 质检单编号
                    'employee_id': processing.employee_id.id,  # 作业人员id
                    'employee_name': processing.employee_id.name,  # 作业人员姓名
                    'processing_date': processing.processing_date.strftime(
                        '%Y-%m-%d') if processing.processing_date else '',  # 业务日期
                    'quantity': processing.quantity,  # 处理数量
                    'state': processing.state,  # 状态
                    'line_ids': [{
                        'product_id': line.product_id.id,  # 产品id
                        'product_name': line.product_id.name,  # 产品名称
                        'unqualified_qty': line.unqualified_qty,  # 不合格数量
                        'note': line.note  # 不合格原因
                    } for line in processing.line_ids]
                })

            return {
                'state': 'success',
                'msgs': '获取成功',
                'data': result,
                'total': total,
                'page_total': total_page,
                'page_no': page_no,
                'page_size': page_size
            }
        except Exception as e:
            return {'state': 'error', 'msgs': str(e)}

    @http.route('/roke/wylj/get_quality_processing_repair', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def get_quality_processing_repair(self):
        """
        获取质检处理单列表 -- 返修单
        """
        try:
            _self = http.request
            # processing_type = _self.jsonrequest.get('processing_type', False)
            quality_order_id = _self.jsonrequest.get('quality_order_id', False)
            search_value = _self.jsonrequest.get('search_value', False)
            page_no = _self.jsonrequest.get('page_no', 1)
            page_size = _self.jsonrequest.get('page_size', 10)

            # domain = []
            # if processing_type:
            #     domain.append(('processing_type', '=', processing_type))
            domain = []

            if quality_order_id:
                domain.append(('quality_order_id', '=', int(quality_order_id)))
            if search_value:
                domain.append(('code', 'ilike', search_value))

            # domain.append(('processing_type', '=', "返修单"))

            processings = _self.env['roke.quality.processing'].search(domain)

            # 分页处理
            total = len(processings)
            total_page = math.ceil(total / page_size) if page_size else 0
            if page_size:
                processings = processings[(page_no - 1) * page_size: page_no * page_size]

            result = []
            for processing in processings:
                result.append({
                    'id': processing.id,  # 质检处理单id
                    'code': processing.code,  # 质检处理单编号
                    'processing_type': processing.processing_type,  # 类型
                    'quality_order_id': processing.quality_order_id.id,  # 质检单id
                    'quality_order_code': processing.quality_order_id.code,  # 质检单编号
                    'employee_id': processing.employee_id.id,  # 作业人员id
                    'employee_name': processing.employee_id.name,  # 作业人员姓名
                    'processing_date': processing.processing_date.strftime(
                        '%Y-%m-%d') if processing.processing_date else '',  # 业务日期
                    'quantity': processing.quantity,  # 处理数量
                    'state': processing.state,  # 状态
                    'line_ids': [{
                        'product_id': line.product_id.id,  # 产品id
                        'product_name': line.product_id.name,  # 产品名称
                        'unqualified_qty': line.unqualified_qty,  # 不合格数量
                        'note': line.note  # 不合格原因
                    } for line in processing.line_ids]
                })

            return {
                'state': 'success',
                'msgs': '获取成功',
                'data': result,
                'total': total,
                'page_total': total_page,
                'page_no': page_no,
                'page_size': page_size
            }
        except Exception as e:
            return {'state': 'error', 'msgs': str(e)}

    @http.route('/roke/wylj/get_quality_processing_scrap', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def get_quality_processing_scrap(self):
        """
        获取质检处理单列表 -- 报废单
        """
        try:
            _self = http.request
            # processing_type = _self.jsonrequest.get('processing_type', False)
            quality_order_id = _self.jsonrequest.get('quality_order_id', False)
            search_value = _self.jsonrequest.get('search_value', False)
            page_no = _self.jsonrequest.get('page_no', 1)
            page_size = _self.jsonrequest.get('page_size', 10)

            # domain = []
            # if processing_type:
            #     domain.append(('processing_type', '=', processing_type))

            domain = []
            if quality_order_id:
                domain.append(('quality_order_id', '=', int(quality_order_id)))
            if search_value:
                domain.append(('code', 'ilike', search_value))

            # domain.append(('processing_type', '=', "报废单"))

            processings = _self.env['roke.quality.processing'].search(domain)

            # 分页处理
            total = len(processings)
            total_page = math.ceil(total / page_size) if page_size else 0
            if page_size:
                processings = processings[(page_no - 1) * page_size: page_no * page_size]

            result = []
            for processing in processings:
                result.append({
                    'id': processing.id,  # 质检处理单id
                    'code': processing.code,  # 质检处理单编号
                    'processing_type': processing.processing_type,  # 类型
                    'quality_order_id': processing.quality_order_id.id,  # 质检单id
                    'quality_order_code': processing.quality_order_id.code,  # 质检单编号
                    'employee_id': processing.employee_id.id,  # 作业人员id
                    'employee_name': processing.employee_id.name,  # 作业人员姓名
                    'processing_date': processing.processing_date.strftime(
                        '%Y-%m-%d') if processing.processing_date else '',  # 业务日期
                    'quantity': processing.quantity,  # 处理数量
                    'state': processing.state,  # 状态
                    'line_ids': [{
                        'product_id': line.product_id.id,  # 产品id
                        'product_name': line.product_id.name,  # 产品名称
                        'unqualified_qty': line.unqualified_qty,  # 不合格数量
                        'note': line.note  # 不合格原因
                    } for line in processing.line_ids]
                })

            return {
                'state': 'success',
                'msgs': '获取成功',
                'data': result,
                'total': total,
                'page_total': total_page,
                'page_no': page_no,
                'page_size': page_size
            }
        except Exception as e:
            return {'state': 'error', 'msgs': str(e)}

    @http.route('/roke/wylj/create_quality_processing_sorting', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def create_quality_processing(self):
        """
        创建质检处理单 -- 分选单
        """
        try:
            _self = http.request
            # processing_type = _self.jsonrequest.get('processing_type', False)
            task_id = _self.jsonrequest.get('task_id', False)
            picking_id = _self.jsonrequest.get('picking_id', False)
            sale_picking_id = _self.jsonrequest.get('sale_picking_id', False)
            employee_id = _self.jsonrequest.get('employee_id', False)
            processing_date = _self.jsonrequest.get('processing_date', False)
            quantity = _self.jsonrequest.get('quantity', 0)
            quality_order_id = _self.jsonrequest.get('quality_order_id', False)
            line_ids = _self.jsonrequest.get('line_ids', [])

            # if not processing_type:
            #     return {"state": "error", "msgs": "处理类型不能为空"}
            if not quality_order_id:
                return {"state": "error", "msgs": "质检单不能为空"}

            processing = _self.env['roke.quality.processing'].create({
                'processing_type': "分选单",
                'task_id': task_id,
                'picking_id': picking_id,
                'sale_picking_id': sale_picking_id,
                'employee_id': employee_id,
                'processing_date': processing_date,
                'quantity': quantity,
                'quality_order_id': quality_order_id,
                'line_ids': [(0, 0, {
                    'product_id': line.get('product_id'),
                    'unqualified_qty': line.get('unqualified_qty'),
                    'note': line.get('note'),
                }) for line in line_ids]
            })

            return {"state": "success", "msgs": "创建成功", "processing_id": processing.id}
        except Exception as e:
            return {"state": "error", "msgs": str(e)}

    @http.route('/roke/wylj/create_quality_processing_scrap', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def create_quality_processing_scrap(self):
        """
        创建质检处理单 -- 报废单
        """
        try:
            _self = http.request
            # processing_type = _self.jsonrequest.get('processing_type', False)
            task_id = _self.jsonrequest.get('task_id', False)
            picking_id = _self.jsonrequest.get('picking_id', False)
            sale_picking_id = _self.jsonrequest.get('sale_picking_id', False)
            employee_id = _self.jsonrequest.get('employee_id', False)
            processing_date = _self.jsonrequest.get('processing_date', False)
            quantity = _self.jsonrequest.get('quantity', 0)
            quality_order_id = _self.jsonrequest.get('quality_order_id', False)
            line_ids = _self.jsonrequest.get('line_ids', [])

            # if not processing_type:
            #     return {"state": "error", "msgs": "处理类型不能为空"}
            if not quality_order_id:
                return {"state": "error", "msgs": "质检单不能为空"}

            processing = _self.env['roke.quality.processing'].create({
                'processing_type': "报废单",
                'task_id': task_id,
                'picking_id': picking_id,
                'sale_picking_id': sale_picking_id,
                'employee_id': employee_id,
                'processing_date': processing_date,
                'quantity': quantity,
                'quality_order_id': quality_order_id,
                'line_ids': [(0, 0, {
                    'product_id': line.get('product_id'),
                    'unqualified_qty': line.get('unqualified_qty'),
                    'note': line.get('note'),
                }) for line in line_ids]
            })

            return {"state": "success", "msgs": "创建成功", "processing_id": processing.id}
        except Exception as e:
            return {"state": "error", "msgs": str(e)}

    @http.route('/roke/wylj/create_quality_processing_repair', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def create_quality_processing_repair(self):
        """
        创建质检处理单 -- 返修单
        """
        try:
            _self = http.request
            # processing_type = _self.jsonrequest.get('processing_type', False)
            task_id = _self.jsonrequest.get('task_id', False)
            picking_id = _self.jsonrequest.get('picking_id', False)
            sale_picking_id = _self.jsonrequest.get('sale_picking_id', False)
            employee_id = _self.jsonrequest.get('employee_id', False)
            processing_date = _self.jsonrequest.get('processing_date', False)
            quantity = _self.jsonrequest.get('quantity', 0)
            quality_order_id = _self.jsonrequest.get('quality_order_id', False)
            line_ids = _self.jsonrequest.get('line_ids', [])

            # if not processing_type:
            #     return {"state": "error", "msgs": "处理类型不能为空"}
            if not quality_order_id:
                return {"state": "error", "msgs": "质检单不能为空"}

            processing = _self.env['roke.quality.processing'].create({
                'processing_type': "返修单",
                'task_id': task_id,
                'picking_id': picking_id,
                'sale_picking_id': sale_picking_id,
                'employee_id': employee_id,
                'processing_date': processing_date,
                'quantity': quantity,
                'quality_order_id': quality_order_id,
                'line_ids': [(0, 0, {
                    'product_id': line.get('product_id'),
                    'unqualified_qty': line.get('unqualified_qty'),
                    'note': line.get('note'),
                }) for line in line_ids]
            })

            return {"state": "success", "msgs": "创建成功", "processing_id": processing.id}
        except Exception as e:
            return {"state": "error", "msgs": str(e)}

    @http.route('/roke/wylj/done_work_order', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def done_work_order(self):
        """
        完工逻辑
        """
        try:
            _self = http.request
            order_id = _self.jsonrequest.get('id', False)
            processing_duration = _self.jsonrequest.get('processing_duration', False)
            total_working_hours = _self.jsonrequest.get('total_working_hours', False)
            employee_id = _self.jsonrequest.get('employee_id', False)
            start_time = _self.jsonrequest.get('start_time', False)

            if not order_id:
                return {"state": "error", "msgs": "工单id不能为空"}
            order = _self.env['roke.work.order'].search([('id', '=', order_id), ('state', '=', '自检完成')])

            if not order:
                return {"state": "error", "msgs": "工单不存在或状态不是自检完成"}

            order.write({
                'state': "已完工",
                'processing_duration': processing_duration,
                'total_working_hours': total_working_hours,
                'employee_id': employee_id,
                'start_time': start_time
            })
            return {"state": "success", "msgs": "完工成功"}
        except Exception as e:
            return {"state": "error", "msgs": str(e)}

    @http.route('/roke/wylj/update_work_order_state_paused', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def update_work_order_state_paused(self):
        """
        更新工单状态为暂停
        """
        try:
            _self = http.request
            work_order_code = _self.jsonrequest.get('work_order_code', False)
            work_order_id = _self.jsonrequest.get('work_order_id', False)

            if not work_order_code:
                return {"state": "error", "msgs": "工单编号不能为空"}

            work_order_obj = _self.env['roke.work.order']
            work_order = work_order_obj.search([('code', '=', work_order_code)])

            if len(work_order) != 1:
                return {"state": "error", "msgs": f"工单编号为{work_order_code}的工单不存在或存在多个"}
            if work_order.state != '自检完成':
                return {"state": "error", "msgs": "工单未自检，无需暂停！"}
            work_order.update_state_paused()

            return {"state": "success", "msgs": "暂停工单成功"}
        except Exception as e:
            return {"state": "error", "msgs": str(e)}

    @http.route('/roke/wylj/update_work_order_state_unfinished', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def update_work_order_state_unfinished(self):
        """
        更新工单状态为自检完成
        """
        try:
            _self = http.request
            work_order_code = _self.jsonrequest.get('work_order_code', False)
            work_order_id = _self.jsonrequest.get('work_order_id', False)

            if not work_order_code:
                return {"state": "error", "msgs": "工单编号不能为空"}

            work_order_obj = _self.env['roke.work.order']
            work_order = work_order_obj.search([('code', '=', work_order_code)])

            if len(work_order) != 1:
                return {"state": "error", "msgs": f"工单编号为{work_order_code}的工单不存在或存在多个"}

            work_order.update_state_unfinished()

            return {"state": "success", "msgs": "取消暂停工单成功"}
        except Exception as e:
            return {"state": "error", "msgs": str(e)}

    @http.route('/roke/wylj/get_order_quality_task_patrol', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def get_order_quality_task_patrol(self):
        """通过生产任务获取巡检单的数据"""
        try:
            _self = http.request
            task_id = _self.jsonrequest.get('task_id', False)

            if not task_id:
                return {"state": "error", "msgs": "生产任务id不能为空"}

            task_obj = _self.env['roke.production.task']
            task = task_obj.browse(int(task_id))
            if not task:
                return {"state": "error", "msgs": "生产任务不存在"}
            data = {
                "task_id": task.id,  # 生产任务id
                "employee_ids": _self.env.user.employee_id.id,  # 发起人
                "plan_qty": task.finish_qty,  # 报检数量
                "product_id": task.product_id.id,  # 产品id
                "product_name": task.product_id.name,  # 产品名称
                "workshop_id": task.workshop_id.id,  # 车间id
                "workshop_name": task.workshop_id.name,  # 车间名称
                "quality_type": "巡检",  # 质检类型
                "scheme_id": task.product_id.atrol_scheme_id.id,  # 质检方案id
                "scheme_name": task.product_id.atrol_scheme_id.name,  # 质检方案名称
                "line_ids": [{
                    "quality_item_id": line.quality_item_id.id,  # 质检项id
                    "quality_item_name": line.quality_item_id.name,  # 质检项名称
                    "analysis_method": line.analysis_method,  # 分析方法
                    "quality_method_id": line.quality_method_id.id,  # 质检方法id
                    "quality_method_name": line.quality_method_id.name,  # 质检方法名称
                } for line in task.product_id.atrol_scheme_id.line_ids],  # 质检方案明细
            }

            return {"state": "success", "msgs": "获取成功", "data": data}
        except Exception as e:
            return {"state": "error", "msgs": str(e)}

    @http.route('/roke/wylj/create_order_quality_patrol', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def create_order_quality_patrol(self):
        """巡检任务创建接口"""
        try:
            _self = http.request
            task_id = _self.jsonrequest.get('task_id', False)
            order_date = _self.jsonrequest.get('order_date', False)
            employee_ids = _self.jsonrequest.get('employee_ids', False)
            plan_qty = _self.jsonrequest.get('plan_qty', False)
            product_id = _self.jsonrequest.get('product_id', False)
            workshop_id = _self.jsonrequest.get('workshop_id', False)
            quality_type = _self.jsonrequest.get('quality_type', False)
            scheme_id = _self.jsonrequest.get('scheme_id', False)
            line_ids = _self.jsonrequest.get('line_ids', False)

            if not task_id:
                return {"state": "error", "msgs": "生产任务id不能为空"}

            if not plan_qty:
                return {"state": "error", "msgs": "报检数量不能为空"}

            if not product_id:
                return {"state": "error", "msgs": "产品id不能为空"}

            if not quality_type:
                return {"state": "error", "msgs": "质检类型不能为空"}

            if not scheme_id:
                return {"state": "error", "msgs": "质检方案id不能为空"}

            if not line_ids:
                return {"state": "error", "msgs": "质检方案明细不能为空"}

            quality_order_obj = _self.env['roke.quality.order']
            # 创建记录
            quality_order = quality_order_obj.create({
                "task_id": task_id,
                "employee_ids": [(6, 0, employee_ids if isinstance(employee_ids, list) else [employee_ids])],
                "plan_qty": float(plan_qty),
                "product_id": product_id,
                "workshop_id": workshop_id,
                "quality_type": quality_type,
                "scheme_id": scheme_id,
                "order_date": order_date,
                "line_ids": [(0, 0, {
                    "item_id": line.get("quality_item_id"),
                }) for line in line_ids],
            })

            return {"state": "success", "msgs": "创建成功", "data": quality_order.code}
        except Exception as e:
            return {"state": "error", "msgs": str(e)}

    @http.route('/roke/wylj/get_quality_task', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def get_quality_task(self):
        """
        获取质检质检单生产任务数据列表接口
        """
        try:
            _self = http.request
            search_value = _self.jsonrequest.get('search_value', False)
            page_no = _self.jsonrequest.get('page_no', 1)
            page_size = _self.jsonrequest.get('page_size', 10)

            domain = []

            if search_value:
                domain.append(('code', 'ilike', search_value))

            task = _self.env['roke.production.task'].search(domain)

            # 分页处理
            total = len(task)
            total_page = math.ceil(total / page_size) if page_size else 0
            if page_size:
                task = task[(page_no - 1) * page_size: page_no * page_size]

            result = []
            for t in task:
                result.append({
                    "task_id": t.id, # 生产任务id
                    "task_code": t.code, # 生产任务编号
                })

            return {
                'state': 'success',
                'msgs': '获取成功',
                'data': result,
                'total': total,
                'page_total': total_page,
                'page_no': page_no,
                'page_size': page_size
            }
        except Exception as e:
            return {'state': 'error', 'msgs': str(e)}

    @http.route('/roke/wylj/debug/task', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def debug_task(self):
        """设备开合模调试任务"""
        jsonrequest = http.request.jsonrequest
        equipment_id = jsonrequest.get('equipment_id', False)
        work_order_id = jsonrequest.get('work_order_id', False)
        if not all([equipment_id, work_order_id]):
            return {"state": "error", "msgs": "设备id和工单id不能为空"}
        equipment = http.request.env["roke.mes.equipment"].browse(int(equipment_id))
        if not equipment:
            return {"state": "error", "msgs": "设备不存在"}
        if not equipment.item_line_id:
            return {"state": "error", "msgs": "设备未设置开合模调试项"}
        work_order = http.request.env["roke.work.order"].browse(int(work_order_id))
        if not work_order:
            return {"state": "error", "msgs": "工单不存在"}
        if not work_order.container_record_ids:
            return {"state": "error", "msgs": "未绑定容器，不可调试！"}
        work_order.write({"state": "调试中"})
        schedule_line = http.request.env["roke.equipment.production.capacity.schedule.line"].sudo().search([
            ("product_id", "=", work_order.product_id.id),
            ("schedule_id.equipment_id", "=", equipment.id),
            ("mold_id", "=", work_order.mold_id.id),
            ("tool_id", "=", work_order.tool_id.id),
        ], limit=1)
        max_debug_times = 0
        if schedule_line:
            max_debug_times = schedule_line.max_debug_times
        if len(work_order.work_debug_record_ids) >= max_debug_times:
            return {"state": "error", "msgs": "调试次数已达到上限"}
        val = self.get_item_current_value(equipment.item_line_id.item_id, equipment)
        
        work_debug_record = http.request.env["roke.work.debug.record"].sudo().create({
            "work_order_id": work_order.id,
            "value": val,
            "time": fields.Datetime.now()
        })
        data = {
            "value": work_debug_record.value,
            "time": work_debug_record.time
        }
        return {"state": "success", "msgs": "获取成功", "data": data}
    
    @http.route('/roke/wylj/debug/finish', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def debug_finish(self):
        """设备调试完成"""
        jsonrequest = http.request.jsonrequest
        work_order_id = jsonrequest.get('work_order_id', False)
        if not work_order_id:
            return {"state": "error", "msgs": "工单id不能为空"}
        work_order = http.request.env["roke.work.order"].browse(int(work_order_id))
        if not work_order:
            return {"state": "error", "msgs": "工单不存在"}
        work_order.write({"state": "调试完成"})
        return {"state": "success", "msgs": "调试完成"}


