<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="view_roke_wylj_inherit_production_task_form" model="ir.ui.view">
        <field name="name">roke.wylj.inherit.production.task.form</field>
        <field name="model">roke.work.order</field>
        <field name="inherit_id" ref="roke_mes_production.view_roke_work_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='deadline']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='salary_type']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//label[@for='salary_rule']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='salary_rule']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='team_salary_type']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='collection_item_ids']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
             <xpath expr="//button[@name='work_order_transfer']" position="after">
                <button name="create_work_record" type="object" string="模拟报工"  class="oe_highlight"
                            attrs="{'invisible': [ ('state', '=', '已完工')]}"/>
            </xpath>


            <xpath expr="//field[@name='team_id']" position="after">
                <field name="mold_id"/>
                <field name="tool_id"/>
                <field name="equipment_id"/>
                <field name="employee_id"/>
                <field name="unqualified_qty"/>
            </xpath>
            <xpath expr="//field[@name='type']" position="after">
                <field name="priority"/>
                <field name="processing_duration"/>
            </xpath>
            <xpath expr="//field[@name='plan_date']" position="after">
                <field name="start_time"/>
            </xpath>
            <xpath expr="//field[@name='collection_scheme_id']" position="after">
                <field name="total_working_hours"/>
            </xpath>
            <xpath expr="//field[@name='state']" position="replace">
                <field name="state" widget="statusbar" statusbar_visible="未完工,调试中,调试完成,自检完成,暂停,已完工"/>
            </xpath>

            <xpath expr="//notebook" position="inside">
                <page string="容器更换记录">
                    <field name="container_record_ids">
                        <tree editable="bottom" create="false" delete="false">
                            <field name="code"/>
                            <field name="finish_qty"/>
                            <field name="finish_time"/>
                        </tree>
                    </field>
                </page>
            </xpath>
        </field>
    </record>
</odoo>